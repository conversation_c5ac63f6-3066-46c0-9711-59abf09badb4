"""
Database dependencies and session management for the application.
"""

from typing import Generator

from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker

from common.settings import settings


def get_db_session() -> Generator[Session, None, None]:
    """Get database session for direct PostgreSQL access."""
    engine = create_engine(settings.database.connection_string)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()
