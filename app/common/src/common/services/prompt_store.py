from __future__ import annotations

import uuid
from dataclasses import dataclass
from datetime import datetime
from typing import Any, List, Optional, Type
from urllib.parse import urlparse

from llama_index.core.bridge.pydantic import Field, PrivateAttr
from llama_index.core.schema import BaseComponent
from sqlalchemy import Column, Index, Integer, UniqueConstraint, create_engine, select, text
from sqlalchemy.dialects.postgresql import TEXT, TIMESTAMP, UUID
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import declarative_base, sessionmaker

from common.settings import settings
from common.utils import get_current_time


@dataclass
class PromptEntry:
    id: int
    client_guid: uuid.UUID
    prompt: str
    created_at: datetime
    updated_at: datetime


def get_data_model(base: Type[Any], table_name: str, schema_name: str) -> Type[Any]:
    class AbstractData(base):  # pylint: disable=too-few-public-methods
        __abstract__ = True
        id = Column(Integer, primary_key=True, autoincrement=True)
        client_guid = Column(UUID(as_uuid=True), nullable=False)
        prompt = Column(TEXT, nullable=False)
        created_at = Column(TIMESTAMP, nullable=False)
        updated_at = Column(TIMESTAMP, nullable=False)

    return type(
        f"Data{table_name}",
        (AbstractData,),
        {
            "__tablename__": table_name,
            "__table_args__": (
                UniqueConstraint("id", name=f"{table_name}:unique_id"),
                Index(f"{table_name}:idx_client", "client_guid"),
                {"schema": schema_name},
            ),
        },
    )


class PromptStore(BaseComponent):
    table_name: str = Field(default="prompts", description="Postgres table name.")
    schema_name: str = Field(default="public", description="Postgres schema name.")

    _table_class: Any = PrivateAttr()
    _session: sessionmaker[Any] = PrivateAttr()
    _async_session: sessionmaker[Any] = PrivateAttr()

    def __init__(
        self,
        session: sessionmaker[Any],
        async_session: sessionmaker[Any],
        table_name: str = "prompts",
        schema_name: str = "public",
    ) -> None:
        super().__init__(table_name=table_name.lower(), schema_name=schema_name.lower())
        base = declarative_base()
        self._table_class = get_data_model(base, table_name, schema_name)
        self._session = session
        self._async_session = async_session
        self._initialize(base)

    @classmethod
    def from_params(
        cls,
        host: Optional[str] = None,
        port: Optional[str] = None,
        database: Optional[str] = None,
        user: Optional[str] = None,
        password: Optional[str] = None,
        table_name: str = "prompts",
        schema_name: str = "public",
        connection_string: Optional[str] = None,
        async_connection_string: Optional[str] = None,
        debug: bool = False,
    ) -> "PromptStore":
        conn_str = connection_string or f"postgresql+psycopg://{user}:{password}@{host}:{port}/{database}"
        async_conn_str = async_connection_string or (f"postgresql+asyncpg://{user}:{password}@{host}:{port}/{database}")
        session, async_session = cls._connect(conn_str, async_conn_str, debug)
        return cls(
            session=session,
            async_session=async_session,
            table_name=table_name,
            schema_name=schema_name,
        )

    @classmethod
    def from_uri(
        cls,
        uri: str,
        db_name: str,
        table_name: str = "prompts",
        schema_name: str = "public",
        debug: bool = False,
    ) -> "PromptStore":
        result = urlparse(uri)
        params = {
            "user": result.username,
            "password": result.password,
            "host": result.hostname,
            "port": result.port if result.port else 5432,
        }
        return cls.from_params(
            **params,
            database=db_name,
            table_name=table_name,
            schema_name=schema_name,
            debug=debug,
        )

    @classmethod
    def _connect(cls, connection_string: str, async_connection_string: str, debug: bool) -> tuple[sessionmaker[Any], sessionmaker[Any]]:
        engine = create_engine(connection_string, echo=debug)
        session = sessionmaker(engine)

        async_engine = create_async_engine(async_connection_string)
        async_session = sessionmaker(async_engine, class_=AsyncSession)
        return session, async_session

    def _create_schema_if_not_exists(self) -> None:
        with self._session() as session, session.begin():
            stmt = text(f"SELECT schema_name FROM information_schema.schemata WHERE schema_name = '{self.schema_name}'")
            result = session.execute(stmt).fetchone()
            if not result:
                create_stmt = text(f"CREATE SCHEMA IF NOT EXISTS {self.schema_name}")
                session.execute(create_stmt)
            session.commit()

    def _create_tables_if_not_exists(self, base: Type[Any]) -> None:
        with self._session() as session, session.begin():
            base.metadata.create_all(session.connection())

    def _initialize(self, base: Type[Any]) -> None:
        self._create_schema_if_not_exists()
        self._create_tables_if_not_exists(base)

    def create_prompt(self, client_guid: uuid.UUID, prompt: str) -> int:
        with self._session() as session:
            now = get_current_time()
            entry = self._table_class(
                client_guid=client_guid,
                prompt=prompt,
                created_at=now,
                updated_at=now,
            )
            session.add(entry)
            session.commit()
            return entry.id

    async def async_create_prompt(self, client_guid: uuid.UUID, prompt: str) -> int:
        async with self._async_session() as session:
            now = get_current_time()
            entry = self._table_class(
                client_guid=client_guid,
                prompt=prompt,
                created_at=now,
                updated_at=now,
            )
            session.add(entry)
            await session.commit()
            return entry.id

    def list_prompts(self, client_guid: uuid.UUID) -> List[PromptEntry]:
        with self._session() as session:
            stmt = select(self._table_class).where(self._table_class.client_guid == client_guid).order_by(self._table_class.id)
            rows = session.execute(stmt).scalars().all()
            return [
                PromptEntry(
                    id=row.id,
                    client_guid=row.client_guid,
                    prompt=row.prompt,
                    created_at=row.created_at,
                    updated_at=row.updated_at,
                )
                for row in rows
            ]

    async def async_list_prompts(self, client_guid: uuid.UUID) -> List[PromptEntry]:
        async with self._async_session() as session:
            stmt = select(self._table_class).where(self._table_class.client_guid == client_guid).order_by(self._table_class.id)
            result = await session.execute(stmt)
            rows = result.scalars().all()
            return [
                PromptEntry(
                    id=row.id,
                    client_guid=row.client_guid,
                    prompt=row.prompt,
                    created_at=row.created_at,
                    updated_at=row.updated_at,
                )
                for row in rows
            ]

    def get_prompt(self, prompt_id: int, client_guid: uuid.UUID) -> PromptEntry:
        with self._session() as session:
            row = session.query(self._table_class).filter_by(id=prompt_id, client_guid=client_guid).first()
            if not row:
                raise LookupError("Prompt not found")
            return PromptEntry(
                id=row.id,
                client_guid=row.client_guid,
                prompt=row.prompt,
                created_at=row.created_at,
                updated_at=row.updated_at,
            )

    async def async_get_prompt(self, prompt_id: int, client_guid: uuid.UUID) -> PromptEntry:
        async with self._async_session() as session:
            stmt = select(self._table_class).where(
                self._table_class.id == prompt_id,
                self._table_class.client_guid == client_guid,
            )
            result = await session.execute(stmt)
            row = result.scalar_one_or_none()
            if not row:
                raise LookupError("Prompt not found")
            return PromptEntry(
                id=row.id,
                client_guid=row.client_guid,
                prompt=row.prompt,
                created_at=row.created_at,
                updated_at=row.updated_at,
            )

    def update_prompt(self, prompt_id: int, client_guid: uuid.UUID, prompt: str) -> None:
        with self._session() as session:
            row = session.query(self._table_class).filter_by(id=prompt_id, client_guid=client_guid).first()
            if not row:
                raise LookupError("Prompt not found")
            row.prompt = prompt
            row.updated_at = get_current_time()
            session.commit()

    async def async_update_prompt(self, prompt_id: int, client_guid: uuid.UUID, prompt: str) -> None:
        async with self._async_session() as session:
            stmt = select(self._table_class).where(
                self._table_class.id == prompt_id,
                self._table_class.client_guid == client_guid,
            )
            result = await session.execute(stmt)
            row = result.scalar_one_or_none()
            if not row:
                raise LookupError("Prompt not found")
            row.prompt = prompt
            row.updated_at = get_current_time()
            await session.commit()

    def delete_prompt(self, prompt_id: int, client_guid: uuid.UUID) -> None:
        with self._session() as session:
            row = session.query(self._table_class).filter_by(id=prompt_id, client_guid=client_guid).first()
            if not row:
                raise LookupError("Prompt not found")
            session.delete(row)
            session.commit()

    async def async_delete_prompt(self, prompt_id: int, client_guid: uuid.UUID) -> None:
        async with self._async_session() as session:
            stmt = select(self._table_class).where(
                self._table_class.id == prompt_id,
                self._table_class.client_guid == client_guid,
            )
            result = await session.execute(stmt)
            row = result.scalar_one_or_none()
            if not row:
                raise LookupError("Prompt not found")
            await session.delete(row)
            await session.commit()

    @classmethod
    def get_instance(cls) -> "PromptStore":
        return cls.from_uri(
            uri=settings.database.connection_string,
            db_name=settings.database.db_name,
            table_name="prompts",
        )
