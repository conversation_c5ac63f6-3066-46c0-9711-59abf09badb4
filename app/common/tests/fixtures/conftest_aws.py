"""AWS and cloud service-related test fixtures."""

import os
from typing import Any, Dict, Generator
from unittest.mock import Mock, patch

import boto3
import pytest
from common.s3_storage import S3Storage

from .constants import (
    DEFAULT_REGION,
    TEST_BUCKET_NAME,
    TEST_JWT_AUDIENCE,
    TEST_JWT_ISSUER,
    TEST_JWT_KEY,
)


class MockProvider:
    """
    Provider for mocking AWS SSM requests.
    """

    def _ssm_setup(self) -> None:
        """
        Mocks param store setup with better error handling.
        """
        try:
            region_name = os.environ.get("AWS_REGION", DEFAULT_REGION)
            self.ssm_client = boto3.client("ssm", region_name=region_name)  # type: ignore

            parameters = [
                ("/alpha/test/global/Web/Jwt/Key", os.environ.get("JWT_KEY", TEST_JWT_KEY), "SecureString"),
                ("/alpha/test/global/Web/Jwt/Issuer", os.environ.get("JWT_ISSUER", TEST_JWT_ISSUER), "String"),
                ("/alpha/test/global/Web/Jwt/Audience", os.environ.get("JWT_AUDIENCE", TEST_JWT_AUDIENCE), "String"),
            ]

            for name, value, param_type in parameters:
                # Type ignore for boto3 parameter type issue
                self.ssm_client.put_parameter(
                    Name=name,
                    Value=value,
                    Type=param_type,  # type: ignore[arg-type]
                )

            assert hasattr(self.ssm_client, "meta")  # type: ignore
            assert self.ssm_client.meta.service_model.service_name == "ssm"  # type: ignore

        except Exception as e:
            pytest.fail(f"Failed to set up SSM mock: {e}")

    def set_up_mock(self) -> None:
        """
        Mocks param store setup.
        """
        self._ssm_setup()


# Environment setup
@pytest.fixture
def setup_test_environment() -> Generator[None, None, None]:
    """Set up test environment variables."""
    test_env = {
        "AWS_REGION": DEFAULT_REGION,
        "env_name": "test",
        "jwt_key": TEST_JWT_KEY,
        "jwt_issuer": TEST_JWT_ISSUER,
        "jwt_audience": TEST_JWT_AUDIENCE,
    }

    # Set environment variables
    for key, value in test_env.items():
        os.environ[key] = value

    yield

    # Clean up environment variables
    for key in test_env.keys():
        os.environ.pop(key, None)


# S3-specific fixtures
@pytest.fixture
def mock_s3_client() -> Generator[Mock, None, None]:
    """Mock S3 client for testing."""
    with patch("common.s3_storage.boto3.client") as mock_boto_client:
        mock_client = Mock()
        mock_boto_client.return_value = mock_client
        yield mock_client


@pytest.fixture
def s3_storage_instance(mock_s3_client: Mock):
    """Create an S3Storage instance with mocked client."""
    return S3Storage(bucket_name=TEST_BUCKET_NAME, prefix="test-prefix")


@pytest.fixture
def s3_storage_default_instance(mock_s3_client: Mock):
    """Create an S3Storage instance with default settings and mocked client."""
    return S3Storage(bucket_name=TEST_BUCKET_NAME)


@pytest.fixture
def sample_s3_data() -> Dict[str, Dict[str, Any]]:
    """Sample data for S3 operations."""
    return {
        "simple": {"result": "success", "value": 123},
        "complex": {
            "job_id": "test-job-123",
            "results": [{"item_id": 1, "status": "completed"}, {"item_id": 2, "status": "failed", "error": "timeout"}],
            "metadata": {"processed_at": "2023-10-01T12:00:00Z", "total_items": 2},
        },
    }


@pytest.fixture
def s3_test_keys():
    """Common S3 keys used in testing."""
    return {
        "simple": "test-item-123",
        "job_result": "jobs/test-job-456/result.json",
        "file_upload": "uploads/test-file.txt",
        "nonexistent": "nonexistent-key",
    }
