"""Test data-related fixtures."""

import uuid
from datetime import datetime
from typing import Any, Dict, List

import pytest

from .constants import (
    TEST_API_REQUESTS,
    TEST_BUCKET_NAME,
    TEST_COMPLETION_TOKENS,
    TEST_DATE,
    TEST_DATE_RANGE,
    TEST_KEY,
    TEST_LLM_PROVIDER_OPENAI,
    TEST_MAX_TOKENS_DEFAULT,
    TEST_MAX_TOKENS_LARGE,
    TEST_MODEL_GPT4,
    TEST_MODEL_GPT35,
    TEST_MODEL_GROUP_OPENAI,
    TEST_PRODUCTION_TAG,
    TEST_PROMPT_TOKENS,
    TEST_SPEND_AMOUNT,
    TEST_SUCCESS_RATE,
    TEST_TAG_NAME,
    TEST_TEMPERATURE_CREATIVE,
    TEST_TEMPERATURE_DEFAULT,
    TEST_TEMPERATURE_PRECISE,
    TEST_TOTAL_TOKENS,
    TEST_TRANSCRIPTION_JOB_ID,
    TEST_TRANSCRIPTION_JOB_ID_SHORT,
    TEST_AUDIO_FILENAME,
    TEST_AUDIO_FILENAME_WAV,
    TEST_S3_AUDIO_URL,
    TEST_S3_AUDIO_URL_MP3,
    TEST_LANGUAGE_CODE_EN,
    TEST_LANGUAGE_CODE_ES,
    TEST_LANGUAGE_CODE_FR,
    TEST_MEDIA_FORMAT_MP3,
    TEST_MEDIA_FORMAT_WAV,
    TEST_USER_ID,
    TEST_USER_ID_123,
    TEST_USER_ID_456,
)

# Test data fixtures


@pytest.fixture
def sample_client_guid() -> uuid.UUID:
    """Sample client GUID for testing."""
    return uuid.UUID("12345678-1234-5678-9abc-123456789012")


@pytest.fixture
def sample_completion_request() -> Dict[str, Any]:
    """Sample CompletionRequest data for testing."""
    return {
        "model": TEST_MODEL_GPT35,
        "messages": ["Test completion request"],
        "max_tokens": TEST_MAX_TOKENS_DEFAULT,
        "temperature": TEST_TEMPERATURE_DEFAULT,
        "files": None
    }


@pytest.fixture
def sample_chat_completion_request() -> Dict[str, Any]:
    """Sample ChatCompletionRequest data for testing."""
    return {
        "model": TEST_MODEL_GPT4,
        "messages": [{"role": "user", "content": "Test chat message"}],
        "max_tokens": 150,
        "temperature": TEST_TEMPERATURE_CREATIVE,
        "files": None
    }


@pytest.fixture
def sample_completion_request_with_files() -> Dict[str, Any]:
    """Sample CompletionRequest with files for testing."""
    return {
        "model": TEST_MODEL_GPT4,
        "messages": ["Analyze these documents"],
        "max_tokens": TEST_MAX_TOKENS_LARGE,
        "temperature": TEST_TEMPERATURE_PRECISE,
        "files": [f"s3://{TEST_BUCKET_NAME}/doc1.pdf", f"s3://{TEST_BUCKET_NAME}/doc2.txt"]
    }


@pytest.fixture
def sample_chat_messages() -> List[Dict[str, str]]:
    """Sample chat messages for testing."""
    return [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "What's the weather like?"},
        {"role": "assistant", "content": "I don't have access to current weather data."},
        {"role": "user", "content": "Can you help with something else?"}
    ]


@pytest.fixture
def sample_user_guid() -> uuid.UUID:
    """Sample user GUID for testing."""
    return uuid.UUID("*************-8765-cba9-************")


# Additional model test fixtures
@pytest.fixture
def sample_job_ids() -> Dict[str, str]:
    """Sample job IDs for testing."""
    return {
        "uuid_job": "12345678-1234-5678-9abc-123456789012",
        "string_job": "batch-job-2023-10-01-001",
        "transcription_job": "transcribe-job-audio-001",
    }


@pytest.fixture
def sample_tag_usage_data() -> Dict[str, Any]:
    """Sample tag usage data for testing."""
    return {
        "tag": TEST_PRODUCTION_TAG,
        "total_spend": 1250.75,
        "total_requests": 5000,
        "total_tokens": 250000,
        "success_rate": 99.8,
        "date_range": TEST_DATE_RANGE,
    }


@pytest.fixture
def sample_tag_detail_data() -> Dict[str, Any]:
    """Sample tag detail data for testing."""
    return {
        "id": uuid.uuid4(),
        "tag": TEST_TAG_NAME,
        "date": TEST_DATE,
        "api_key": TEST_KEY,
        "model": TEST_MODEL_GPT4,
        "model_group": TEST_MODEL_GROUP_OPENAI,
        "custom_llm_provider": TEST_LLM_PROVIDER_OPENAI,
        "prompt_tokens": TEST_PROMPT_TOKENS,
        "completion_tokens": TEST_COMPLETION_TOKENS,
        "cache_read_input_tokens": 0,
        "cache_creation_input_tokens": 0,
        "spend": 0.05,
        "api_requests": 1,
        "successful_requests": 1,
        "failed_requests": 0,
        "created_at": datetime(2023, 10, 1, 10, 0, 0),
        "updated_at": datetime(2023, 10, 1, 10, 30, 0),
    }


@pytest.fixture
def sample_transcription_data() -> Dict[str, Any]:
    """Sample transcription request data for testing."""
    return {"s3_url": "s3://bucket/audio/meeting-2023-10-01.wav", "language_code": "en-US", "media_format": "wav", "user": "test_user_123"}


@pytest.fixture
def sample_batch_status_data() -> Dict[str, Any]:
    """Sample batch job status data for testing."""
    return {
        "job_id": "batch-job-status-test",
        "status": "IN_PROGRESS",
        "status_description": "Processing batch items",
        "progress": {"processed": 75, "total": 100, "percentage": 75.0},
        "created_at": "2023-10-01T10:00:00Z",
        "updated_at": "2023-10-01T10:30:00Z",
        "started_at": "2023-10-01T10:05:00Z",
        "completed_at": None,
    }


@pytest.fixture
def sample_batch_result_data() -> Dict[str, Any]:
    """Sample batch job result data for testing."""
    return {
        "job_id": "batch-job-result-test",
        "status": "COMPLETED",
        "result_urls": ["s3://results/batch-job-result-test/output-1.json", "s3://results/batch-job-result-test/output-2.json"],
        "pagination": {"page": 1, "per_page": 50, "total": 100, "pages": 2},
    }


@pytest.fixture
def invalid_uuid_strings() -> List[str]:
    """Invalid UUID strings for testing validation."""
    return [
        "invalid-uuid",
        "12345678-1234-5678-9abc",  # Too short
        "12345678-1234-5678-9abc-123456789012-extra",  # Too long
        "gggggggg-1234-5678-9abc-123456789012",  # Invalid characters
        "",  # Empty string
        "not-a-uuid-at-all",
    ]


@pytest.fixture
def edge_case_strings() -> Dict[str, str]:
    """Edge case strings for testing."""
    return {
        "empty": "",
        "whitespace": "   ",
        "unicode": "测试数据 🤖 émojis",
        "special_chars": "!@#$%^&*()_+-=[]{}|;':\",./<>?",
        "very_long": "x" * 1000,
        "newlines": "line1\nline2\r\nline3",
        "tabs": "col1\tcol2\tcol3",
    }


@pytest.fixture
def tag_usage_by_date_data() -> Dict[str, Any]:
    """Sample TagUsageByDate data for testing."""
    return {
        "date": TEST_DATE,
        "spend": TEST_SPEND_AMOUNT,
        "api_requests": TEST_API_REQUESTS,
        "total_tokens": TEST_TOTAL_TOKENS,
        "success_rate": TEST_SUCCESS_RATE,
    }


@pytest.fixture
def tag_usage_by_model_data() -> Dict[str, Any]:
    """Sample TagUsageByModel data for testing."""
    return {
        "model": TEST_MODEL_GPT4,
        "model_group": TEST_MODEL_GROUP_OPENAI,
        "custom_llm_provider": TEST_LLM_PROVIDER_OPENAI,
        "total_spend": 875.52,
        "total_requests": 3500,
        "total_tokens": 175000,
        "success_rate": 99.9,
    }


# Transcription-specific fixtures

@pytest.fixture
def sample_transcription_request_minimal() -> Dict[str, str]:
    """Minimal transcription request data for testing."""
    return {"file": TEST_S3_AUDIO_URL_MP3}


@pytest.fixture
def sample_transcription_request_complete() -> Dict[str, str]:
    """Complete transcription request data for testing."""
    return {
        "file": TEST_S3_AUDIO_URL,
        "language_code": TEST_LANGUAGE_CODE_EN,
        "media_format": TEST_MEDIA_FORMAT_WAV,
        "user": TEST_USER_ID
    }


@pytest.fixture
def sample_transcription_job_response() -> Dict[str, str]:
    """Sample transcription job response data for testing."""
    return {"job_id": TEST_TRANSCRIPTION_JOB_ID}


@pytest.fixture
def sample_transcription_job_response_short() -> Dict[str, str]:
    """Sample transcription job response with short ID for testing."""
    return {"job_id": TEST_TRANSCRIPTION_JOB_ID_SHORT}


@pytest.fixture
def sample_upload_file_mock():
    """Mock UploadFile for testing."""
    import io
    from fastapi import UploadFile
    return UploadFile(filename=TEST_AUDIO_FILENAME, file=io.BytesIO(b"mock audio content"))


@pytest.fixture
def sample_upload_file_wav_mock():
    """Mock UploadFile with WAV format for testing."""
    import io
    from fastapi import UploadFile
    return UploadFile(filename=TEST_AUDIO_FILENAME_WAV, file=io.BytesIO(b"mock wav audio content"))


@pytest.fixture
def sample_transcription_request_variants() -> Dict[str, Dict[str, str]]:
    """Multiple transcription request variants for testing."""
    return {
        "english": {
            "file": TEST_S3_AUDIO_URL,
            "language_code": TEST_LANGUAGE_CODE_EN,
            "media_format": TEST_MEDIA_FORMAT_WAV,
            "user": TEST_USER_ID
        },
        "spanish": {
            "file": TEST_S3_AUDIO_URL_MP3,
            "language_code": TEST_LANGUAGE_CODE_ES,
            "media_format": TEST_MEDIA_FORMAT_MP3,
            "user": TEST_USER_ID_123
        },
        "french": {
            "file": TEST_S3_AUDIO_URL,
            "language_code": TEST_LANGUAGE_CODE_FR,
            "media_format": TEST_MEDIA_FORMAT_MP3,
            "user": TEST_USER_ID_456
        }
    }
