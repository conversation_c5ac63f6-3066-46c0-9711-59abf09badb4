"""Database-related test fixtures."""

import uuid
from typing import Any

import pytest
from common.services.job_store import BatchJobStatusEnum
from llama_index.core.llms import ChatMessage

from .constants import TEST_AGENT_NAME


@pytest.fixture
def test_uuids() -> dict[str, uuid.UUID]:
    """Provide consistent UUIDs for testing."""
    return {
        "client_1": uuid.UUID("12345678-1234-5678-9abc-123456789abc"),
        "client_2": uuid.UUID("*************-8765-cba9-987654321cba"),
        "user_1": uuid.UUID("11111111-**************-************"),
        "user_2": uuid.UUID("*************-8888-9999-aaaaaaaaaaaa"),
        "agent_1": uuid.UUID("aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee"),
        "agent_2": uuid.UUID("bbbbbbbb-cccc-dddd-eeee-ffffffffffff"),
        "job_1": uuid.UUID("ffffffff-0000-1111-2222-************"),
    }


@pytest.fixture
def sample_agent_data() -> dict[str, Any]:
    """Provide sample agent data for testing."""
    return {
        "name": TEST_AGENT_NAME,
        "definition": {
            "type": "workflow",
            "steps": [
                {"action": "analyze", "parameters": {"depth": "deep"}},
                {"action": "generate", "parameters": {"format": "json"}},
            ],
            "metadata": {"version": "1.0", "author": "test_user"},
        },
    }


@pytest.fixture
def sample_chat_messages() -> list[ChatMessage]:
    """Provide sample chat messages for testing."""
    return [
        ChatMessage(role="user", content="Hello, how are you?"),
        ChatMessage(role="assistant", content="I'm doing well, thank you! How can I help you today?"),
        ChatMessage(role="user", content="Can you help me with a coding problem?"),
        ChatMessage(role="assistant", content="Of course! I'd be happy to help with your coding problem."),
    ]


@pytest.fixture
def sample_job_data(test_uuids: dict[str, uuid.UUID]) -> dict[str, Any]:
    """Provide sample job data for testing."""
    return {
        "client_guid": test_uuids["client_1"],
        "total_count": 10,
        "job_params": {
            "model": "gpt-3.5-turbo",
            "temperature": 0.7,
            "max_tokens": 1000,
            "batch_size": 5,
        },
        "status": BatchJobStatusEnum.PENDING,
    }


@pytest.fixture
def sample_prompt_data(test_uuids: dict[str, uuid.UUID]) -> dict[str, Any]:
    """Provide sample prompt data for testing."""
    return {
        "client_guid": test_uuids["client_1"],
        "prompt": "You are a helpful AI assistant. Please respond to user queries with accurate and helpful information.",
    }


@pytest.fixture
def mock_agent_store_session():
    """Provide mocked session for AgentStore unit tests."""
    from unittest.mock import MagicMock

    mock_session = MagicMock()
    mock_session_instance = MagicMock()
    mock_session.return_value.__enter__.return_value = mock_session_instance
    mock_session.return_value.__exit__.return_value = None
    return mock_session, mock_session_instance


@pytest.fixture
def mock_agent_store_async_session():
    """Provide mocked async session for AgentStore unit tests."""
    from unittest.mock import AsyncMock, MagicMock

    mock_async_session = MagicMock()
    mock_async_session_instance = MagicMock()
    mock_async_session_instance.commit = AsyncMock()
    mock_async_session_instance.execute = AsyncMock()
    mock_async_session.return_value.__aenter__.return_value = mock_async_session_instance
    mock_async_session.return_value.__aexit__.return_value = None
    return mock_async_session, mock_async_session_instance


@pytest.fixture
def agent_store_test_data() -> dict[str, Any]:
    """Provide comprehensive test data for AgentStore testing."""
    return {
        "simple_agent": {"name": "simple_test_agent", "definition": {"type": "simple", "action": "echo"}},
        "complex_agent": {
            "name": "complex_workflow_agent",
            "definition": {
                "type": "workflow",
                "steps": [
                    {"action": "analyze", "parameters": {"depth": "deep"}},
                    {"action": "generate", "parameters": {"format": "json"}},
                    {"action": "validate", "parameters": {"schema": "strict"}},
                ],
                "metadata": {"version": "2.0", "author": "test_user", "tags": ["production", "validated"], "timeout": 300},
            },
        },
        "minimal_agent": {"name": "minimal", "definition": {}},
        "unicode_agent": {"name": "测试代理_🤖", "definition": {"description": "Unicode test with émojis 🚀"}},
    }


@pytest.fixture
def agent_store_edge_cases() -> dict[str, Any]:
    """Provide edge case data for AgentStore testing."""
    return {
        "long_name": {
            "name": "a" * 1000,  # Very long name
            "definition": {"type": "test"},
        },
        "large_definition": {
            "name": "large_def_agent",
            "definition": {"data": ["x"] * 10000},  # Large definition
        },
        "special_chars": {"name": "agent!@#$%^&*()", "definition": {"special": "!@#$%^&*()_+-=[]{}|;':\",./<>?"}},
        "empty_name": {"name": "", "definition": {"type": "empty"}},
    }


@pytest.fixture
def mock_database_table():
    """Provide a mock database table for testing."""
    from datetime import datetime
    from unittest.mock import MagicMock

    def create_mock_entry(agent_id: uuid.UUID, name: str, definition: dict[str, Any]):
        mock_entry = MagicMock()
        mock_entry.id = agent_id
        mock_entry.name = name
        mock_entry.definition = definition
        mock_entry.created_at = datetime.now()
        mock_entry.updated_at = datetime.now()
        return mock_entry

    return create_mock_entry


@pytest.fixture
def mock_chat_store_session():
    """Provide mocked session for ChatStore unit tests."""
    from unittest.mock import MagicMock

    mock_session = MagicMock()
    mock_session_instance = MagicMock()
    mock_session.return_value.__enter__.return_value = mock_session_instance
    mock_session.return_value.__exit__.return_value = None
    return mock_session, mock_session_instance


@pytest.fixture
def mock_chat_store_async_session():
    """Provide mocked async session for ChatStore unit tests."""
    from unittest.mock import AsyncMock, MagicMock

    mock_async_session = MagicMock()
    mock_async_session_instance = MagicMock()
    mock_async_session_instance.commit = AsyncMock()
    mock_async_session_instance.execute = AsyncMock()
    mock_async_session.return_value.__aenter__.return_value = mock_async_session_instance
    mock_async_session.return_value.__aexit__.return_value = None
    return mock_async_session, mock_async_session_instance


@pytest.fixture
def chat_store_test_data() -> dict[str, Any]:
    """Provide comprehensive test data for ChatStore testing."""
    return {
        "simple_messages": [
            ChatMessage(role="user", content="Hello, how are you?"),
            ChatMessage(role="assistant", content="I'm doing well, thank you!"),
        ],
        "complex_conversation": [
            ChatMessage(role="system", content="You are a helpful AI assistant."),
            ChatMessage(role="user", content="Can you help me with a coding problem?"),
            ChatMessage(role="assistant", content="Of course! I'd be happy to help with your coding problem."),
            ChatMessage(role="user", content="I need to debug this Python function."),
            ChatMessage(role="assistant", content="Please share the function and I'll help you debug it."),
        ],
        "unicode_messages": [
            ChatMessage(role="user", content="Hello 🌟 测试 émojis and spëcial chars: !@#$%^&*()"),
            ChatMessage(role="assistant", content="I can handle unicode: 🚀 αβγ 한글 العربية"),
        ],
        "empty_conversation": [],
    }


@pytest.fixture
def chat_store_edge_cases() -> dict[str, Any]:
    """Provide edge case data for ChatStore testing."""
    return {
        "very_long_message": [
            ChatMessage(role="user", content="x" * 10000),  # Very long message
        ],
        "special_characters": [
            ChatMessage(role="user", content="!@#$%^&*()_+-=[]{}|;':\",./<>?"),
        ],
        "newlines_and_tabs": [
            ChatMessage(role="user", content="Line 1\nLine 2\r\nLine 3\tTab separated"),
        ],
        "empty_content": [
            ChatMessage(role="user", content=""),
        ],
    }


@pytest.fixture
def mock_chat_database_table():
    """Provide a mock database table for chat store testing."""
    from datetime import datetime
    from unittest.mock import MagicMock

    def create_mock_chat_entry(client_guid: uuid.UUID, user_guid: uuid.UUID, messages: list[ChatMessage]):
        mock_entry = MagicMock()
        mock_entry.client_guid = client_guid
        mock_entry.user_guid = user_guid
        mock_entry.messages = [msg.model_dump() for msg in messages]
        mock_entry.created_at = datetime.now()
        mock_entry.updated_at = datetime.now()
        return mock_entry

    return create_mock_chat_entry


@pytest.fixture
def mock_job_store_session():
    """Provide mocked session for JobStore unit tests."""
    from unittest.mock import MagicMock

    mock_session = MagicMock()
    mock_session_instance = MagicMock()
    mock_session.return_value.__enter__.return_value = mock_session_instance
    mock_session.return_value.__exit__.return_value = None
    return mock_session, mock_session_instance


@pytest.fixture
def mock_job_store_async_session():
    """Provide mocked async session for JobStore unit tests."""
    from unittest.mock import AsyncMock, MagicMock

    mock_async_session = MagicMock()
    mock_async_session_instance = MagicMock()
    mock_async_session_instance.commit = AsyncMock()
    mock_async_session_instance.execute = AsyncMock()
    mock_async_session.return_value.__aenter__.return_value = mock_async_session_instance
    mock_async_session.return_value.__aexit__.return_value = None
    return mock_async_session, mock_async_session_instance


@pytest.fixture
def job_store_test_data(test_uuids: dict[str, uuid.UUID]) -> dict[str, Any]:
    """Provide comprehensive test data for JobStore testing."""
    return {
        "simple_job": {
            "client_guid": test_uuids["client_1"],
            "total_count": 5,
            "job_params": {"model": "gpt-3.5-turbo", "temperature": 0.7},
            "status": BatchJobStatusEnum.PENDING,
        },
        "complex_job": {
            "client_guid": test_uuids["client_1"],
            "total_count": 100,
            "job_params": {
                "model": "gpt-4",
                "temperature": 0.8,
                "max_tokens": 2000,
                "system_prompt": "You are a helpful assistant",
                "batch_config": {"batch_size": 10, "retry_count": 3},
                "output_config": {"format": "json", "compression": "gzip"},
            },
            "status": BatchJobStatusEnum.PROCESSING,
        },
        "minimal_job": {
            "client_guid": test_uuids["client_2"],
            "total_count": 1,
            "job_params": {},
            "status": BatchJobStatusEnum.PENDING,
        },
        "completed_job": {
            "client_guid": test_uuids["client_1"],
            "total_count": 10,
            "job_params": {"model": "gpt-3.5-turbo"},
            "status": BatchJobStatusEnum.COMPLETED,
        },
    }


@pytest.fixture
def job_store_edge_cases(test_uuids: dict[str, uuid.UUID]) -> dict[str, Any]:
    """Provide edge case data for JobStore testing."""
    return {
        "large_params": {
            "client_guid": test_uuids["client_1"],
            "total_count": 1000,
            "job_params": {"data": ["x"] * 5000},  # Large parameter data
            "status": BatchJobStatusEnum.PENDING,
        },
        "unicode_params": {
            "client_guid": test_uuids["client_2"],
            "total_count": 1,
            "job_params": {"prompt": "测试 🤖 émojis special chars: !@#$%^&*()"},
            "status": BatchJobStatusEnum.PENDING,
        },
        "zero_count": {
            "client_guid": test_uuids["client_1"],
            "total_count": 0,
            "job_params": {"model": "gpt-3.5-turbo"},
            "status": BatchJobStatusEnum.PENDING,
        },
        "no_client": {
            "client_guid": None,
            "total_count": 5,
            "job_params": {"model": "gpt-3.5-turbo"},
            "status": BatchJobStatusEnum.PENDING,
        },
    }


@pytest.fixture
def mock_job_database_table():
    """Provide a mock database table for job store testing."""
    from datetime import datetime
    from unittest.mock import MagicMock

    def create_mock_job_entry(
        job_id: uuid.UUID,
        client_guid: uuid.UUID,
        total_count: int,
        job_params: dict[str, Any],
        status: BatchJobStatusEnum = BatchJobStatusEnum.PENDING,
        processed_count: int = 0,
    ):
        mock_entry = MagicMock()
        mock_entry.id = job_id
        mock_entry.client_guid = client_guid
        mock_entry.total_count = total_count
        mock_entry.processed_count = processed_count
        mock_entry.job_params = job_params
        mock_entry.status = status
        mock_entry.status_description = None
        mock_entry.results = {}
        mock_entry.created_at = datetime.now()
        mock_entry.updated_at = datetime.now()
        mock_entry.started_at = None
        mock_entry.completed_at = None
        return mock_entry

    return create_mock_job_entry


@pytest.fixture
def mock_prompt_store_session():
    """Provide mocked session for PromptStore unit tests."""
    from unittest.mock import MagicMock

    mock_session = MagicMock()
    mock_session_instance = MagicMock()
    mock_session.return_value.__enter__.return_value = mock_session_instance
    mock_session.return_value.__exit__.return_value = None
    return mock_session, mock_session_instance


@pytest.fixture
def mock_prompt_store_async_session():
    """Provide mocked async session for PromptStore unit tests."""
    from unittest.mock import AsyncMock, MagicMock

    mock_async_session = MagicMock()
    mock_async_session_instance = MagicMock()
    mock_async_session_instance.commit = AsyncMock()
    mock_async_session_instance.execute = AsyncMock()
    mock_async_session.return_value.__aenter__.return_value = mock_async_session_instance
    mock_async_session.return_value.__aexit__.return_value = None
    return mock_async_session, mock_async_session_instance


@pytest.fixture
def prompt_store_test_data(test_uuids: dict[str, uuid.UUID]) -> dict[str, Any]:
    """Provide comprehensive test data for PromptStore testing."""
    return {
        "simple_prompt": {
            "client_guid": test_uuids["client_1"],
            "prompt": "You are a helpful AI assistant. Please respond to user queries with accurate information.",
        },
        "complex_prompt": {
            "client_guid": test_uuids["client_1"],
            "prompt": """You are an advanced AI assistant with expertise in multiple domains.

Guidelines:
1. Always provide accurate and helpful information
2. If uncertain, clearly state your limitations
3. Use proper formatting and structure in your responses
4. Be concise yet comprehensive
5. Consider multiple perspectives when applicable

Context: You are helping users with complex technical and analytical tasks.
Style: Professional, clear, and engaging.
""",
        },
        "minimal_prompt": {
            "client_guid": test_uuids["client_2"],
            "prompt": "Help",
        },
        "system_prompt": {
            "client_guid": test_uuids["client_1"],
            "prompt": "You are a system assistant. Follow all instructions carefully and maintain security protocols.",
        },
    }


@pytest.fixture
def prompt_store_edge_cases(test_uuids: dict[str, uuid.UUID]) -> dict[str, Any]:
    """Provide edge case data for PromptStore testing."""
    return {
        "unicode_prompt": {
            "client_guid": test_uuids["client_1"],
            "prompt": "Hello 🌟 测试 émojis and spëcial chars: !@#$%^&*()",
        },
        "large_prompt": {
            "client_guid": test_uuids["client_2"],
            "prompt": "This is a very long prompt. " * 1000,  # ~28KB
        },
        "special_chars": {
            "client_guid": test_uuids["client_1"],
            "prompt": "Special characters: !@#$%^&*()_+-=[]{}|;':\",./<>?\\`~",
        },
        "newlines_and_formatting": {
            "client_guid": test_uuids["client_2"],
            "prompt": "Line 1\nLine 2\r\nLine 3\t\tTabbed content\n\n  Spaced content  \n",
        },
        "empty_prompt": {
            "client_guid": test_uuids["client_1"],
            "prompt": "",
        },
    }


@pytest.fixture
def mock_prompt_database_table():
    """Provide a mock database table for prompt store testing."""
    from datetime import datetime
    from unittest.mock import MagicMock

    def create_mock_prompt_entry(
        prompt_id: int,
        client_guid: uuid.UUID,
        prompt: str,
    ):
        mock_entry = MagicMock()
        mock_entry.id = prompt_id
        mock_entry.client_guid = client_guid
        mock_entry.prompt = prompt
        mock_entry.created_at = datetime.now()
        mock_entry.updated_at = datetime.now()
        return mock_entry

    return create_mock_prompt_entry
