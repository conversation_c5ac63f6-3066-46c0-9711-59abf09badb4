"""Shared constants for tests."""

from typing import Final

# AWS related test constants
DEFAULT_REGION: Final[str] = "us-east-1"

# S3 related test constants
TEST_BUCKET_NAME: Final[str] = "aca-test-encore-us-east-1"
TEST_JOB_ID: Final[str] = "job-123"
TEST_KEY: Final[str] = "test-key"
TEST_CONTENT_TYPE: Final[str] = "text/plain"

# JWT related test constants
TEST_JWT_KEY: Final[str] = "test-jwt-secret-key-for-testing-only"
TEST_JWT_ISSUER: Final[str] = "aca-test-issuer"
TEST_JWT_AUDIENCE: Final[str] = "aca-test-audience"

# Test data paths and content
TEST_PDF_PATH: Final[str] = "test_data/test-docx.pdf"
TEST_PPTX_PATH: Final[str] = "test_data/test-pptx.pdf"
TEST_PDF_CONTENT: Final[bytes] = b"Test PDF Content"
TEST_PPTX_CONTENT: Final[bytes] = b"Test PPTX Content"

# Job Store related test constants
TEST_JOB_TABLE_NAME: Final[str] = "batch_jobs"
TEST_JOB_SCHEMA_NAME: Final[str] = "public"
TEST_CUSTOM_JOB_TABLE_NAME: Final[str] = "custom_batch_jobs"
TEST_CUSTOM_JOB_SCHEMA_NAME: Final[str] = "custom_schema"
TEST_URI_JOB_TABLE_NAME: Final[str] = "uri_batch_jobs"

# Agent Store related test constants
TEST_AGENT_NAME: Final[str] = "test_agent"
TEST_AGENT_TABLE_NAME: Final[str] = "agents"
TEST_AGENT_SCHEMA_NAME: Final[str] = "public"
TEST_CUSTOM_AGENT_TABLE_NAME: Final[str] = "custom_agents"
TEST_CUSTOM_AGENT_SCHEMA_NAME: Final[str] = "custom_schema"
TEST_URI_AGENT_TABLE_NAME: Final[str] = "uri_agents"

# Chat Store related test constants
TEST_CHAT_TABLE_NAME: Final[str] = "chatstore"
TEST_CHAT_SCHEMA_NAME: Final[str] = "public"
TEST_CUSTOM_CHAT_TABLE_NAME: Final[str] = "custom_chatstore"
TEST_CUSTOM_CHAT_SCHEMA_NAME: Final[str] = "custom_schema"
TEST_URI_CHAT_TABLE_NAME: Final[str] = "uri_chatstore"

# Prompt Store related test constants
TEST_PROMPT_TABLE_NAME: Final[str] = "prompts"
TEST_PROMPT_SCHEMA_NAME: Final[str] = "public"
TEST_CUSTOM_PROMPT_TABLE_NAME: Final[str] = "custom_prompts"
TEST_CUSTOM_PROMPT_SCHEMA_NAME: Final[str] = "custom_schema"
TEST_URI_PROMPT_TABLE_NAME: Final[str] = "uri_prompts"

# Database related test constants
TEST_DB_HOST: Final[str] = "localhost"
TEST_DB_PORT: Final[str] = "5432"
TEST_DB_NAME: Final[str] = "test_db"
TEST_DB_USER: Final[str] = "test_user"
TEST_DB_PASSWORD: Final[str] = "test_pass"
TEST_DB_CONNECTION_STRING: Final[str] = "postgresql://test:test@localhost:5432/"
TEST_DB_URI: Final[str] = "postgresql://user:pass@localhost:5432/"

# Tag/LLM related test constants
TEST_TAG_NAME: Final[str] = "test-tag"
TEST_PRODUCTION_TAG: Final[str] = "production-api"
TEST_MODEL_GPT4: Final[str] = "gpt-4"
TEST_MODEL_GPT35: Final[str] = "gpt-3.5-turbo"
TEST_MODEL_GROUP_OPENAI: Final[str] = "openai"
TEST_LLM_PROVIDER_OPENAI: Final[str] = "openai"
TEST_DATE: Final[str] = "2023-10-01"
TEST_DATE_RANGE: Final[str] = "2023-10-01 to 2023-10-07"

# Test numeric values for tag usage
TEST_SPEND_AMOUNT: Final[float] = 125.50
TEST_API_REQUESTS: Final[int] = 500
TEST_TOTAL_TOKENS: Final[int] = 25000
TEST_SUCCESS_RATE: Final[float] = 99.2
TEST_PROMPT_TOKENS: Final[int] = 100
TEST_COMPLETION_TOKENS: Final[int] = 50

# LLM request test constants
TEST_MAX_TOKENS_DEFAULT: Final[int] = 100
TEST_MAX_TOKENS_LARGE: Final[int] = 200
TEST_TEMPERATURE_DEFAULT: Final[float] = 0.7
TEST_TEMPERATURE_CREATIVE: Final[float] = 0.8
TEST_TEMPERATURE_PRECISE: Final[float] = 0.5

# Transcription related test constants
TEST_TRANSCRIPTION_JOB_ID: Final[str] = "transcribe-job-12345678-1234-5678-9abc-123456789012"
TEST_TRANSCRIPTION_JOB_ID_SHORT: Final[str] = "transcribe-job-audio-001"
TEST_AUDIO_FILENAME: Final[str] = "test_audio.mp3"
TEST_AUDIO_FILENAME_WAV: Final[str] = "test_audio.wav"
TEST_S3_AUDIO_URL: Final[str] = "s3://bucket/audio.wav"
TEST_S3_AUDIO_URL_MP3: Final[str] = "s3://bucket/audio/file.mp3"
TEST_LANGUAGE_CODE_EN: Final[str] = "en-US"
TEST_LANGUAGE_CODE_ES: Final[str] = "es-ES"
TEST_LANGUAGE_CODE_FR: Final[str] = "fr-FR"
TEST_MEDIA_FORMAT_MP3: Final[str] = "mp3"
TEST_MEDIA_FORMAT_WAV: Final[str] = "wav"
TEST_USER_ID: Final[str] = "test_user"
TEST_USER_ID_123: Final[str] = "user123"
TEST_USER_ID_456: Final[str] = "user456"
