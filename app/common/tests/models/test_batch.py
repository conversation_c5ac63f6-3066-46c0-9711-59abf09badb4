"""Tests for common.models.batch module."""

import uuid

import pytest
from common.models.batch import JobResponse, JobResultResponse, JobStatusResponse
from pydantic import ValidationError


class TestJobResponse:
    """Test JobResponse model."""

    def test_valid_creation(self):
        """Test creating JobResponse with valid UUID."""
        job_id = uuid.uuid4()
        response = JobResponse(job_id=job_id)

        assert response.job_id == job_id
        assert isinstance(response.job_id, uuid.UUID)

    def test_string_uuid_conversion(self):
        """Test JobResponse accepts string UUID."""
        job_id_str = "12345678-1234-5678-9abc-123456789012"
        response = JobResponse(job_id=job_id_str)

        assert response.job_id == uuid.UUID(job_id_str)

    def test_validation_errors(self):
        """Test JobResponse validation failures."""
        with pytest.raises(ValidationError):
            JobResponse(job_id="invalid-uuid")

        with pytest.raises(ValidationError):
            JobResponse()

    def test_json_serialization(self):
        """Test JobResponse JSON serialization."""
        job_id = uuid.uuid4()
        response = JobResponse(job_id=job_id)

        json_str = response.model_dump_json()
        assert str(job_id) in json_str


class TestJobStatusResponse:
    """Test JobStatusResponse model."""

    def test_minimal_creation(self):
        """Test creating JobStatusResponse with required fields only."""
        response = JobStatusResponse(job_id="test-job", status="PENDING")

        assert response.job_id == "test-job"
        assert response.status == "PENDING"
        assert response.progress == {}

    def test_with_optional_fields(self):
        """Test JobStatusResponse with optional fields."""
        response = JobStatusResponse(
            job_id="test-job", status="IN_PROGRESS", status_description="Processing", progress={"processed": 50, "total": 100}
        )

        assert response.status_description == "Processing"
        assert response.progress["processed"] == 50

    def test_validation_errors(self):
        """Test JobStatusResponse validation failures."""
        with pytest.raises(ValidationError):
            JobStatusResponse(status="PENDING")  # Missing job_id


class TestJobResultResponse:
    """Test JobResultResponse model."""

    def test_minimal_creation(self):
        """Test creating JobResultResponse with required fields only."""
        response = JobResultResponse(job_id="result-job", status="COMPLETED")

        assert response.job_id == "result-job"
        assert response.status == "COMPLETED"
        assert response.result_urls == []

    def test_with_optional_fields(self):
        """Test JobResultResponse with optional fields."""
        response = JobResultResponse(
            job_id="result-job", status="COMPLETED", result_urls=["s3://bucket/file.json"], pagination={"page": 1, "total": 10}
        )

        assert len(response.result_urls) == 1
        assert response.pagination["total"] == 10

    def test_validation_errors(self):
        """Test JobResultResponse validation failures."""
        with pytest.raises(ValidationError):
            JobResultResponse(job_id="test")  # Missing status
