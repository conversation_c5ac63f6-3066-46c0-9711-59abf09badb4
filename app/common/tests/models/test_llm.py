"""Concise tests for common.models.llm module."""

import pytest
from common.models.llm import Chat<PERSON><PERSON>pletionRequest, CompletionRequest
from pydantic import ValidationError

from tests.fixtures.constants import TEST_MODEL_GPT35, TEST_MODEL_GPT4


class TestCompletionRequest:
    """Test cases for CompletionRequest model."""

    def test_create_minimal_request(self, sample_completion_request):
        """Test creating CompletionRequest with minimal required fields."""
        request = CompletionRequest(
            model=sample_completion_request["model"],
            files=sample_completion_request["files"]
        )

        assert request.model == sample_completion_request["model"]
        assert request.files is None
        assert request.max_tokens is None
        assert request.temperature is None

    def test_create_complete_request(self, sample_completion_request):
        """Test CompletionRequest with all common fields populated."""
        request = CompletionRequest(**sample_completion_request)

        assert request.model == sample_completion_request["model"]
        assert request.messages == sample_completion_request["messages"]
        assert request.max_tokens == sample_completion_request["max_tokens"]
        assert request.temperature == sample_completion_request["temperature"]
        assert request.files is None

    def test_create_request_with_files(self, sample_completion_request_with_files):
        """Test CompletionRequest with files field."""
        request = CompletionRequest(**sample_completion_request_with_files)

        assert request.files == sample_completion_request_with_files["files"]
        assert len(request.files) == 2
        assert sample_completion_request_with_files["files"][0] in request.files

    def test_validation_requires_model(self):
        """Test that model field is required."""
        with pytest.raises(ValidationError) as exc_info:
            CompletionRequest(files=None)

        assert "model" in str(exc_info.value)

    def test_json_serialization(self, sample_completion_request):
        """Test CompletionRequest JSON serialization."""
        request = CompletionRequest(**sample_completion_request)

        json_data = request.model_dump()
        assert json_data["model"] == sample_completion_request["model"]
        assert json_data["messages"] == sample_completion_request["messages"]

        # Test excluding None values
        json_exclude_none = request.model_dump(exclude_none=True)
        assert "files" not in json_exclude_none


class TestChatCompletionRequest:
    """Test cases for ChatCompletionRequest model."""

    def test_create_minimal_request(self, sample_chat_completion_request):
        """Test creating ChatCompletionRequest with minimal required fields."""
        request = ChatCompletionRequest(
            model=sample_chat_completion_request["model"],
            messages=sample_chat_completion_request["messages"],
            files=sample_chat_completion_request["files"]
        )

        assert request.model == sample_chat_completion_request["model"]
        assert request.messages == sample_chat_completion_request["messages"]
        assert request.files is None

    def test_create_complete_request(self, sample_chat_completion_request):
        """Test ChatCompletionRequest with all fields populated."""
        request = ChatCompletionRequest(**sample_chat_completion_request)

        assert request.model == sample_chat_completion_request["model"]
        assert request.messages == sample_chat_completion_request["messages"]
        assert request.max_tokens == sample_chat_completion_request["max_tokens"]
        assert request.temperature == sample_chat_completion_request["temperature"]

    def test_complex_conversation(self, sample_chat_messages):
        """Test ChatCompletionRequest with multi-turn conversation."""
        request = ChatCompletionRequest(
            model=TEST_MODEL_GPT4,
            messages=sample_chat_messages,
            files=None
        )

        assert len(request.messages) == 4
        assert request.messages[0]["role"] == "system"
        assert request.messages[-1]["role"] == "user"

    def test_validation_requires_model_and_messages(self):
        """Test that both model and messages are required."""
        with pytest.raises(ValidationError):
            ChatCompletionRequest(messages=[{"role": "user", "content": "Hi"}], files=None)

        with pytest.raises(ValidationError):
            ChatCompletionRequest(model=TEST_MODEL_GPT4, files=None)

    def test_optional_parameters(self):
        """Test ChatCompletionRequest with optional parameters."""
        request = ChatCompletionRequest(
            model=TEST_MODEL_GPT4,
            messages=[{"role": "user", "content": "Hello"}],
            max_tokens=150,
            temperature=0.9,
            top_p=0.95,
            stop=["END"],
            user="test_user",
            files=None
        )

        assert request.max_tokens == 150
        assert request.temperature == 0.9
        assert request.top_p == 0.95
        assert request.stop == ["END"]
        assert request.user == "test_user"

    def test_json_serialization(self, sample_chat_completion_request):
        """Test ChatCompletionRequest JSON serialization."""
        request = ChatCompletionRequest(**sample_chat_completion_request)

        json_data = request.model_dump()
        assert json_data["model"] == sample_chat_completion_request["model"]
        assert json_data["messages"] == sample_chat_completion_request["messages"]

        # Test excluding None values
        json_exclude_none = request.model_dump(exclude_none=True)
        assert "files" not in json_exclude_none


class TestLLMModelsCompatibility:
    """Test compatibility between LLM models."""

    def test_both_models_work_with_same_model_name(self):
        """Test that both models accept the same model names."""
        completion = CompletionRequest(model=TEST_MODEL_GPT35, files=None)
        chat = ChatCompletionRequest(
            model=TEST_MODEL_GPT35,
            messages=[{"role": "user", "content": "Test"}],
            files=None
        )

        assert completion.model == chat.model == TEST_MODEL_GPT35

    def test_serialization_includes_common_fields(self):
        """Test that both models serialize common fields consistently."""
        completion = CompletionRequest(
            model=TEST_MODEL_GPT4,
            messages=["Test"],
            temperature=0.7,
            max_tokens=100,
            files=None
        )

        chat = ChatCompletionRequest(
            model=TEST_MODEL_GPT4,
            messages=[{"role": "user", "content": "Test"}],
            temperature=0.7,
            max_tokens=100,
            files=None
        )

        completion_json = completion.model_dump()
        chat_json = chat.model_dump()

        # Common fields should have same values
        assert completion_json["model"] == chat_json["model"]
        assert completion_json["temperature"] == chat_json["temperature"]
        assert completion_json["max_tokens"] == chat_json["max_tokens"]
