"""Tests for common.models.tags module following best practices."""

import pytest
from common.models.tags import (
    LiteLLMDailyTagSpend,
    TagUsageByDate,
    TagUsageByModel,
    TagUsageDetail,
    TagUsageResponse,
    TagUsageSummary,
)
from pydantic import ValidationError


class TestLiteLLMDailyTagSpend:
    """Test cases for LiteLLMDailyTagSpend SQLAlchemy model."""

    def test_table_configuration(self):
        """Test model table configuration."""
        assert LiteLLMDailyTagSpend.__tablename__ == "LiteLLM_DailyTagSpend"
        assert LiteLLMDailyTagSpend.__table_args__["schema"] == "public"

    def test_required_columns_exist(self):
        """Test that required columns exist on the model."""
        required_columns = [
            "id", "tag", "date", "api_key", "model", "spend",
            "api_requests", "prompt_tokens", "completion_tokens"
        ]
        for column in required_columns:
            assert hasattr(LiteLLMDailyTagSpend, column)


class TestTagUsageSummary:
    """Test cases for TagUsageSummary Pydantic model."""

    def test_valid_creation(self, sample_tag_usage_data):
        """Test creating TagUsageSummary with valid data using fixture."""
        summary = TagUsageSummary(**sample_tag_usage_data)

        assert summary.tag == sample_tag_usage_data["tag"]
        assert summary.total_spend == sample_tag_usage_data["total_spend"]
        assert summary.total_requests == sample_tag_usage_data["total_requests"]
        assert summary.success_rate == sample_tag_usage_data["success_rate"]

    def test_missing_required_field_validation(self):
        """Test validation fails when required fields are missing."""
        with pytest.raises(ValidationError, match="tag"):
            TagUsageSummary(total_spend=100.0)

    @pytest.mark.parametrize("invalid_field,invalid_value", [
        ("total_spend", "not_a_number"),
        ("total_requests", "not_an_int"),
        ("success_rate", "not_a_float"),
    ])
    def test_field_type_validation(self, sample_tag_usage_data, invalid_field, invalid_value):
        """Test validation of field types."""
        data = sample_tag_usage_data.copy()
        data[invalid_field] = invalid_value

        with pytest.raises(ValidationError):
            TagUsageSummary(**data)


class TestTagUsageDetail:
    """Test cases for TagUsageDetail Pydantic model."""

    def test_valid_creation(self, sample_tag_detail_data):
        """Test creating TagUsageDetail with valid data using fixture."""
        detail = TagUsageDetail(**sample_tag_detail_data)

        assert detail.tag == sample_tag_detail_data["tag"]
        assert detail.model == sample_tag_detail_data["model"]
        assert detail.spend == sample_tag_detail_data["spend"]
        assert detail.prompt_tokens == sample_tag_detail_data["prompt_tokens"]

    def test_model_dump_serialization(self, sample_tag_detail_data):
        """Test model serialization to dict."""
        detail = TagUsageDetail(**sample_tag_detail_data)
        json_data = detail.model_dump()

        assert isinstance(json_data, dict)
        assert json_data["model"] == sample_tag_detail_data["model"]
        assert json_data["spend"] == sample_tag_detail_data["spend"]


class TestTagUsageByDate:
    """Test cases for TagUsageByDate Pydantic model."""

    def test_valid_creation(self, tag_usage_by_date_data):
        """Test creating TagUsageByDate with valid data."""
        usage = TagUsageByDate(**tag_usage_by_date_data)

        assert usage.date == tag_usage_by_date_data["date"]
        assert usage.spend == tag_usage_by_date_data["spend"]
        assert usage.api_requests == tag_usage_by_date_data["api_requests"]
        assert usage.success_rate == tag_usage_by_date_data["success_rate"]


class TestTagUsageByModel:
    """Test cases for TagUsageByModel Pydantic model."""

    def test_valid_creation(self, tag_usage_by_model_data):
        """Test creating TagUsageByModel with valid data."""
        usage = TagUsageByModel(**tag_usage_by_model_data)

        assert usage.model == tag_usage_by_model_data["model"]
        assert usage.model_group == tag_usage_by_model_data["model_group"]
        assert usage.total_spend == tag_usage_by_model_data["total_spend"]
        assert usage.total_requests == tag_usage_by_model_data["total_requests"]


class TestTagUsageResponse:
    """Test cases for TagUsageResponse Pydantic model."""

    def test_complete_response_creation(
        self,
        sample_tag_usage_data,
        tag_usage_by_date_data,
        tag_usage_by_model_data
    ):
        """Test creating complete TagUsageResponse with all components."""
        summary = TagUsageSummary(**sample_tag_usage_data)
        daily_data = [TagUsageByDate(**tag_usage_by_date_data)]
        model_breakdown = [TagUsageByModel(**tag_usage_by_model_data)]

        response = TagUsageResponse(
            tag=sample_tag_usage_data["tag"],
            summary=summary,
            daily_data=daily_data,
            model_breakdown=model_breakdown,
            total_records=1000,
        )

        assert response.tag == sample_tag_usage_data["tag"]
        assert response.summary == summary
        assert len(response.daily_data) == 1
        assert len(response.model_breakdown) == 1
        assert response.total_records == 1000

    def test_empty_response_creation(self, sample_tag_usage_data):
        """Test creating TagUsageResponse with empty lists."""
        summary = TagUsageSummary(**sample_tag_usage_data)

        response = TagUsageResponse(
            tag="test-tag",
            summary=summary,
            daily_data=[],
            model_breakdown=[],
            total_records=0,
        )

        assert response.tag == "test-tag"
        assert response.daily_data == []
        assert response.model_breakdown == []
        assert response.total_records == 0
