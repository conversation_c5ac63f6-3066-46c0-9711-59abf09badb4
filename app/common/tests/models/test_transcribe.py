"""Concise tests for common.models.transcribe module."""

import pytest
from common.models.transcribe import TranscriptionJobResponse, TranscriptionRequest
from pydantic import ValidationError

from tests.fixtures.constants import TEST_TRANSCRIPTION_JOB_ID, TEST_S3_AUDIO_URL


class TestTranscriptionRequest:
    """Test cases for TranscriptionRequest model."""

    def test_create_minimal_request(self, sample_transcription_request_minimal):
        """Test creating TranscriptionRequest with minimal required fields."""
        request = TranscriptionRequest(**sample_transcription_request_minimal)

        assert request.file == sample_transcription_request_minimal["file"]
        assert request.language_code is None
        assert request.media_format is None
        assert request.user is None

    def test_create_complete_request(self, sample_transcription_request_complete):
        """Test TranscriptionRequest with all fields populated."""
        request = TranscriptionRequest(**sample_transcription_request_complete)

        assert request.file == sample_transcription_request_complete["file"]
        assert request.language_code == sample_transcription_request_complete["language_code"]
        assert request.media_format == sample_transcription_request_complete["media_format"]
        assert request.user == sample_transcription_request_complete["user"]

    def test_with_upload_file(self, sample_upload_file_mock, sample_transcription_request_complete):
        """Test TranscriptionRequest with UploadFile."""
        request = TranscriptionRequest(
            file=sample_upload_file_mock,
            language_code=sample_transcription_request_complete["language_code"],
            media_format=sample_transcription_request_complete["media_format"],
            user=sample_transcription_request_complete["user"]
        )

        assert request.file == sample_upload_file_mock
        assert request.language_code == sample_transcription_request_complete["language_code"]
        assert request.media_format == sample_transcription_request_complete["media_format"]
        assert request.user == sample_transcription_request_complete["user"]

    def test_validation_requires_file(self):
        """Test that file field is required."""
        with pytest.raises(ValidationError) as exc_info:
            TranscriptionRequest(language_code="en-US", user="test")

        assert "file" in str(exc_info.value)

    def test_as_form_with_upload_file(self, sample_upload_file_mock):
        """Test TranscriptionRequest.as_form with UploadFile."""
        request = TranscriptionRequest.as_form(
            file=sample_upload_file_mock,
            language_code="en-US",
            user="test_user"
        )

        assert request.file == sample_upload_file_mock
        assert request.language_code == "en-US"
        assert request.user == "test_user"

    def test_as_form_with_file_url(self):
        """Test as_form with file_url parameter."""
        request = TranscriptionRequest.as_form(file_url=TEST_S3_AUDIO_URL)

        assert request.file == TEST_S3_AUDIO_URL
        assert request.language_code is None
        assert request.user is None

    def test_as_form_validation_missing_file_and_url(self):
        """Test as_form validation when both file and file_url are missing."""
        with pytest.raises(ValueError) as exc_info:
            TranscriptionRequest.as_form(language_code="en-US", user="test")

        assert "file or file_url must be provided" in str(exc_info.value)


class TestTranscriptionJobResponse:
    """Test cases for TranscriptionJobResponse model."""

    def test_create_response(self, sample_transcription_job_response):
        """Test creating TranscriptionJobResponse."""
        response = TranscriptionJobResponse(**sample_transcription_job_response)

        assert response.job_id == sample_transcription_job_response["job_id"]
        assert isinstance(response.job_id, str)

    def test_validation_requires_job_id(self):
        """Test that job_id field is required."""
        with pytest.raises(ValidationError) as exc_info:
            TranscriptionJobResponse()

        assert "job_id" in str(exc_info.value)

    def test_equality_comparison(self, sample_transcription_job_response_short):
        """Test TranscriptionJobResponse equality comparison."""
        job_id = sample_transcription_job_response_short["job_id"]
        response1 = TranscriptionJobResponse(job_id=job_id)
        response2 = TranscriptionJobResponse(job_id=job_id)
        response3 = TranscriptionJobResponse(job_id="different-job-id")

        assert response1 == response2
        assert response1 != response3


class TestTranscribeModelsIntegration:
    """Integration tests for transcribe models workflow."""

    def test_request_response_workflow(self, sample_transcription_request_complete, sample_transcription_job_response):
        """Test realistic transcription workflow."""
        # Create request
        request = TranscriptionRequest(**sample_transcription_request_complete)

        # Create job response
        response = TranscriptionJobResponse(**sample_transcription_job_response)

        # Verify workflow consistency
        assert request.file == sample_transcription_request_complete["file"]
        assert response.job_id == sample_transcription_job_response["job_id"]

    def test_multiple_request_variants(self, sample_transcription_request_variants):
        """Test multiple transcription request configurations."""
        for variant_name, variant_data in sample_transcription_request_variants.items():
            request = TranscriptionRequest(**variant_data)

            assert request.file == variant_data["file"]
            assert request.language_code == variant_data["language_code"]
            assert request.media_format == variant_data["media_format"]
            assert request.user == variant_data["user"]
