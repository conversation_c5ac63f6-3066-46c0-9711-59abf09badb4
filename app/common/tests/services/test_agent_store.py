"""Tests for AgentStore service following best practices."""

from datetime import datetime
from typing import Any
from unittest.mock import MagicMock, patch

import pytest
from common.services import AgentStore
from common.services.agent_store import AgentEntry
from sqlalchemy.exc import IntegrityError

from ..fixtures.constants import (
    TEST_AGENT_SCHEMA_NAME,
    TEST_AGENT_TABLE_NAME,
    TEST_CUSTOM_AGENT_SCHEMA_NAME,
    TEST_CUSTOM_AGENT_TABLE_NAME,
    TEST_DB_HOST,
    TEST_DB_NAME,
    TEST_DB_PASSWORD,
    TEST_DB_PORT,
    TEST_DB_URI,
    TEST_DB_USER,
    TEST_URI_AGENT_TABLE_NAME,
)


class TestAgentStoreCreation:
    """Test AgentStore instantiation and configuration."""

    @patch("common.services.agent_store.create_engine")
    @patch("common.services.agent_store.create_async_engine")
    @patch("common.services.agent_store.sessionmaker")
    def test_from_params_with_defaults(self, mock_sessionmaker: Any, mock_create_async_engine: Any, mock_create_engine: Any):
        """Test AgentStore creation with default parameters."""
        store = AgentStore.from_params(
            host=TEST_DB_HOST,
            port=TEST_DB_PORT,
            database=TEST_DB_NAME,
            user=TEST_DB_USER,
            password=TEST_DB_PASSWORD,
        )

        assert store.table_name == TEST_AGENT_TABLE_NAME
        assert store.schema_name == TEST_AGENT_SCHEMA_NAME

    @patch("common.services.agent_store.create_engine")
    @patch("common.services.agent_store.create_async_engine")
    @patch("common.services.agent_store.sessionmaker")
    def test_from_params_with_custom_values(self, mock_sessionmaker: Any, mock_create_async_engine: Any, mock_create_engine: Any):
        """Test AgentStore creation with custom table and schema names."""
        store = AgentStore.from_params(
            host=TEST_DB_HOST,
            port=TEST_DB_PORT,
            database=TEST_DB_NAME,
            user=TEST_DB_USER,
            password=TEST_DB_PASSWORD,
            table_name=TEST_CUSTOM_AGENT_TABLE_NAME,
            schema_name=TEST_CUSTOM_AGENT_SCHEMA_NAME,
        )

        assert store.table_name == TEST_CUSTOM_AGENT_TABLE_NAME
        assert store.schema_name == TEST_CUSTOM_AGENT_SCHEMA_NAME

    @patch("common.services.agent_store.create_engine")
    @patch("common.services.agent_store.create_async_engine")
    @patch("common.services.agent_store.sessionmaker")
    def test_from_uri_creation(self, mock_sessionmaker: Any, mock_create_async_engine: Any, mock_create_engine: Any):
        """Test AgentStore creation from URI."""
        store = AgentStore.from_uri(
            uri=TEST_DB_URI,
            db_name=TEST_DB_NAME,
            table_name=TEST_URI_AGENT_TABLE_NAME,
        )

        assert store.table_name == TEST_URI_AGENT_TABLE_NAME
        assert store.schema_name == TEST_AGENT_SCHEMA_NAME


class TestAgentStoreCRUDOperations:
    """Test Create, Read, Update, Delete operations."""

    def test_create_agent_success(self, mock_agent_store_session, mock_database_table, agent_store_test_data, test_uuids):
        """Test successful agent creation."""
        mock_session, mock_session_instance = mock_agent_store_session
        agent_data = agent_store_test_data["simple_agent"]

        # Create mock entry
        mock_entry = mock_database_table(test_uuids["agent_1"], agent_data["name"], agent_data["definition"])

        with (
            patch("common.services.agent_store.create_engine"),
            patch("common.services.agent_store.create_async_engine"),
            patch("common.services.agent_store.sessionmaker", return_value=mock_session),
        ):
            store = AgentStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock(return_value=mock_entry)

            agent_id = store.create_agent(agent_data["name"], agent_data["definition"])

            assert agent_id == test_uuids["agent_1"]
            mock_session_instance.add.assert_called_once()
            # Commit is called during initialization and agent creation
            assert mock_session_instance.commit.call_count >= 1

    def test_get_agent_success(self, mock_agent_store_session, mock_database_table, agent_store_test_data, test_uuids):
        """Test successful agent retrieval."""
        mock_session, mock_session_instance = mock_agent_store_session
        agent_data = agent_store_test_data["complex_agent"]

        # Create mock entry
        mock_entry = mock_database_table(test_uuids["agent_1"], agent_data["name"], agent_data["definition"])
        mock_session_instance.query.return_value.filter_by.return_value.first.return_value = mock_entry

        with (
            patch("common.services.agent_store.create_engine"),
            patch("common.services.agent_store.create_async_engine"),
            patch("common.services.agent_store.sessionmaker", return_value=mock_session),
        ):
            store = AgentStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            retrieved_agent = store.get_agent(test_uuids["agent_1"])

            assert isinstance(retrieved_agent, AgentEntry)
            assert retrieved_agent.id == test_uuids["agent_1"]
            assert retrieved_agent.name == agent_data["name"]
            assert retrieved_agent.definition == agent_data["definition"]
            assert isinstance(retrieved_agent.created_at, datetime)
            assert isinstance(retrieved_agent.updated_at, datetime)

    def test_get_nonexistent_agent_raises_lookup_error(self, mock_agent_store_session, test_uuids):
        """Test that retrieving non-existent agent raises LookupError."""
        mock_session, mock_session_instance = mock_agent_store_session
        mock_session_instance.query.return_value.filter_by.return_value.first.return_value = None

        with (
            patch("common.services.agent_store.create_engine"),
            patch("common.services.agent_store.create_async_engine"),
            patch("common.services.agent_store.sessionmaker", return_value=mock_session),
        ):
            store = AgentStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            with pytest.raises(LookupError, match="Agent not found"):
                store.get_agent(test_uuids["agent_1"])

    @pytest.mark.parametrize("agent_key", ["simple_agent", "complex_agent", "minimal_agent", "unicode_agent"])
    def test_create_multiple_agent_types(self, mock_agent_store_session, mock_database_table, agent_store_test_data, test_uuids, agent_key):
        """Test creating different types of agents."""
        mock_session, mock_session_instance = mock_agent_store_session
        agent_data = agent_store_test_data[agent_key]

        mock_entry = mock_database_table(test_uuids["agent_1"], agent_data["name"], agent_data["definition"])

        with (
            patch("common.services.agent_store.create_engine"),
            patch("common.services.agent_store.create_async_engine"),
            patch("common.services.agent_store.sessionmaker", return_value=mock_session),
        ):
            store = AgentStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock(return_value=mock_entry)

            agent_id = store.create_agent(agent_data["name"], agent_data["definition"])

            assert agent_id == test_uuids["agent_1"]
            mock_session_instance.add.assert_called_once()
            # Commit is called during initialization and agent creation
            assert mock_session_instance.commit.call_count >= 1


class TestAgentStoreAsyncOperations:
    """Test asynchronous operations."""

    @pytest.mark.asyncio
    async def test_async_create_agent_success(self, mock_agent_store_async_session, mock_database_table, agent_store_test_data, test_uuids):
        """Test successful async agent creation."""
        mock_async_session, mock_async_session_instance = mock_agent_store_async_session
        agent_data = agent_store_test_data["simple_agent"]

        mock_entry = mock_database_table(test_uuids["agent_1"], agent_data["name"], agent_data["definition"])

        with (
            patch("common.services.agent_store.create_engine"),
            patch("common.services.agent_store.create_async_engine"),
            patch("common.services.agent_store.sessionmaker") as mock_sessionmaker,
        ):
            # Mock both sync and async sessionmaker calls
            mock_sessionmaker.side_effect = [MagicMock(), mock_async_session]

            store = AgentStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock(return_value=mock_entry)

            agent_id = await store.async_create_agent(agent_data["name"], agent_data["definition"])

            assert agent_id == test_uuids["agent_1"]
            mock_async_session_instance.add.assert_called_once()
            # Commit should be called (async mock doesn't need await for assertion)
            mock_async_session_instance.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_get_agent_success(self, mock_agent_store_async_session, mock_database_table, agent_store_test_data, test_uuids):
        """Test successful async agent retrieval."""
        mock_async_session, mock_async_session_instance = mock_agent_store_async_session
        agent_data = agent_store_test_data["complex_agent"]

        mock_entry = mock_database_table(test_uuids["agent_1"], agent_data["name"], agent_data["definition"])

        # Mock async query result
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_entry
        mock_async_session_instance.execute.return_value = mock_result

        with (
            patch("common.services.agent_store.create_engine"),
            patch("common.services.agent_store.create_async_engine"),
            patch("common.services.agent_store.sessionmaker") as mock_sessionmaker,
        ):
            # Mock both sync and async sessionmaker calls
            mock_sessionmaker.side_effect = [MagicMock(), mock_async_session]

            store = AgentStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            retrieved_agent = await store.async_get_agent(test_uuids["agent_1"])

            assert isinstance(retrieved_agent, AgentEntry)
            assert retrieved_agent.id == test_uuids["agent_1"]
            assert retrieved_agent.name == agent_data["name"]
            assert retrieved_agent.definition == agent_data["definition"]

    @pytest.mark.asyncio
    async def test_async_get_nonexistent_agent_raises_error(self, mock_agent_store_async_session, test_uuids):
        """Test async retrieval of non-existent agent raises LookupError."""
        mock_async_session, mock_async_session_instance = mock_agent_store_async_session

        # Mock empty result
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_async_session_instance.execute.return_value = mock_result

        with (
            patch("common.services.agent_store.create_engine"),
            patch("common.services.agent_store.create_async_engine"),
            patch("common.services.agent_store.sessionmaker") as mock_sessionmaker,
        ):
            mock_sessionmaker.side_effect = [MagicMock(), mock_async_session]

            store = AgentStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            with pytest.raises(LookupError, match="Agent not found"):
                await store.async_get_agent(test_uuids["agent_1"])


class TestAgentStoreEdgeCases:
    """Test edge cases and error scenarios."""

    @pytest.mark.parametrize("edge_case_key", ["long_name", "large_definition", "special_chars"])
    def test_edge_case_data_handling(
        self, mock_agent_store_session, mock_database_table, agent_store_edge_cases, test_uuids, edge_case_key
    ):
        """Test handling of edge case data."""
        mock_session, mock_session_instance = mock_agent_store_session
        edge_data = agent_store_edge_cases[edge_case_key]

        mock_entry = mock_database_table(test_uuids["agent_1"], edge_data["name"], edge_data["definition"])

        with (
            patch("common.services.agent_store.create_engine"),
            patch("common.services.agent_store.create_async_engine"),
            patch("common.services.agent_store.sessionmaker", return_value=mock_session),
        ):
            store = AgentStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock(return_value=mock_entry)

            agent_id = store.create_agent(edge_data["name"], edge_data["definition"])

            assert agent_id == test_uuids["agent_1"]

    def test_database_integrity_error_handling(self, mock_agent_store_session, agent_store_test_data):
        """Test handling of database integrity errors."""
        mock_session, mock_session_instance = mock_agent_store_session
        agent_data = agent_store_test_data["simple_agent"]

        with (
            patch("common.services.agent_store.create_engine"),
            patch("common.services.agent_store.create_async_engine"),
            patch("common.services.agent_store.sessionmaker", return_value=mock_session),
        ):
            store = AgentStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock()

            # Set side effect after initialization to target only agent creation
            mock_session_instance.commit.side_effect = IntegrityError("Duplicate key", {}, None)

            with pytest.raises(IntegrityError):
                store.create_agent(agent_data["name"], agent_data["definition"])


class TestAgentStoreSchemaInitialization:
    """Test schema and table initialization logic."""

    def test_schema_creation_logic(self, mock_agent_store_session):
        """Test schema creation when it doesn't exist."""
        mock_session, mock_session_instance = mock_agent_store_session

        # Mock schema check to return no result (schema doesn't exist)
        mock_session_instance.execute.return_value.fetchone.return_value = None

        with (
            patch("common.services.agent_store.create_engine"),
            patch("common.services.agent_store.create_async_engine"),
            patch("common.services.agent_store.sessionmaker", return_value=mock_session),
        ):
            store = AgentStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            # Verify schema check was performed
            mock_session_instance.execute.assert_called()
            # Verify commit was called (for schema creation)
            mock_session_instance.commit.assert_called()

    def test_existing_schema_handling(self, mock_agent_store_session):
        """Test handling when schema already exists."""
        mock_session, mock_session_instance = mock_agent_store_session

        # Mock schema check to return a result (schema exists)
        mock_result = MagicMock()
        mock_session_instance.execute.return_value.fetchone.return_value = mock_result

        with (
            patch("common.services.agent_store.create_engine"),
            patch("common.services.agent_store.create_async_engine"),
            patch("common.services.agent_store.sessionmaker", return_value=mock_session),
        ):
            store = AgentStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            # Verify schema check was performed
            mock_session_instance.execute.assert_called()
            # Verify commit was still called (for potential table creation)
            mock_session_instance.commit.assert_called()
