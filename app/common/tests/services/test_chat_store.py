"""Tests for PostgresChatStore service following best practices."""

import uuid
from typing import Any
from unittest.mock import MagicMock, patch

import pytest
from common.services import PostgresChatStore
from llama_index.core.llms import ChatMessage
from sqlalchemy.exc import IntegrityError

from ..fixtures.constants import (
    TEST_CHAT_SCHEMA_NAME,
    TEST_CHAT_TABLE_NAME,
    TEST_CUSTOM_CHAT_SCHEMA_NAME,
    TEST_CUSTOM_CHAT_TABLE_NAME,
    TEST_DB_HOST,
    TEST_DB_NAME,
    TEST_DB_PASSWORD,
    TEST_DB_PORT,
    TEST_DB_URI,
    TEST_DB_USER,
    TEST_URI_CHAT_TABLE_NAME,
)


class TestChatStoreCreation:
    """Test PostgresChatStore instantiation and configuration."""

    @patch("common.services.chat_store.create_engine")
    @patch("common.services.chat_store.create_async_engine")
    @patch("common.services.chat_store.sessionmaker")
    def test_from_params_with_defaults(self, mock_sessionmaker: Any, mock_create_async_engine: Any, mock_create_engine: Any):
        """Test ChatStore creation with default parameters."""
        store = PostgresChatStore.from_params(
            host=TEST_DB_HOST,
            port=TEST_DB_PORT,
            database=TEST_DB_NAME,
            user=TEST_DB_USER,
            password=TEST_DB_PASSWORD,
        )

        assert store.table_name == TEST_CHAT_TABLE_NAME
        assert store.schema_name == TEST_CHAT_SCHEMA_NAME

    @patch("common.services.chat_store.create_engine")
    @patch("common.services.chat_store.create_async_engine")
    @patch("common.services.chat_store.sessionmaker")
    def test_from_params_with_custom_values(self, mock_sessionmaker: Any, mock_create_async_engine: Any, mock_create_engine: Any):
        """Test ChatStore creation with custom table and schema names."""
        store = PostgresChatStore.from_params(
            host=TEST_DB_HOST,
            port=TEST_DB_PORT,
            database=TEST_DB_NAME,
            user=TEST_DB_USER,
            password=TEST_DB_PASSWORD,
            table_name=TEST_CUSTOM_CHAT_TABLE_NAME,
            schema_name=TEST_CUSTOM_CHAT_SCHEMA_NAME,
            debug=True,
            use_jsonb=True,
        )

        assert store.table_name == TEST_CUSTOM_CHAT_TABLE_NAME
        assert store.schema_name == TEST_CUSTOM_CHAT_SCHEMA_NAME

    @patch("common.services.chat_store.create_engine")
    @patch("common.services.chat_store.create_async_engine")
    @patch("common.services.chat_store.sessionmaker")
    def test_from_uri_creation(self, mock_sessionmaker: Any, mock_create_async_engine: Any, mock_create_engine: Any):
        """Test ChatStore creation from URI."""
        store = PostgresChatStore.from_uri(
            uri=TEST_DB_URI,
            db_name=TEST_DB_NAME,
            table_name=TEST_URI_CHAT_TABLE_NAME,
            debug=True,
            use_jsonb=True,
        )

        assert store.table_name == TEST_URI_CHAT_TABLE_NAME
        assert store.schema_name == TEST_CHAT_SCHEMA_NAME


class TestChatStoreOperations:
    """Test chat message CRUD operations."""

    def test_get_messages_empty_result(self, mock_chat_store_session, test_uuids: dict[str, uuid.UUID]):
        """Test retrieving messages when no messages exist."""
        mock_session, mock_session_instance = mock_chat_store_session
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # Mock empty result using proper SQLAlchemy pattern
        mock_result = MagicMock()
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = []
        mock_result.scalars.return_value = mock_scalars
        mock_session_instance.execute.return_value = mock_result

        with (
            patch("common.services.chat_store.create_engine"),
            patch("common.services.chat_store.create_async_engine"),
            patch("common.services.chat_store.sessionmaker", return_value=mock_session),
        ):
            store = PostgresChatStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            messages = store.get_messages(client_guid, user_guid)

            assert messages == []
            assert isinstance(messages, list)

    def test_get_messages_with_results(
        self, mock_chat_store_session, test_uuids: dict[str, uuid.UUID], chat_store_test_data: dict[str, Any]
    ):
        """Test retrieving messages with actual results."""
        mock_session, mock_session_instance = mock_chat_store_session
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]
        expected_messages = chat_store_test_data["simple_messages"]

        # Create mock database rows that will be converted to ChatMessage objects
        mock_rows = []
        for msg in expected_messages:
            mock_row = MagicMock()
            mock_row.message = msg.model_dump()
            mock_rows.append(mock_row)

        mock_result = MagicMock()
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = mock_rows
        mock_result.scalars.return_value = mock_scalars
        mock_session_instance.execute.return_value = mock_result

        with (
            patch("common.services.chat_store.create_engine"),
            patch("common.services.chat_store.create_async_engine"),
            patch("common.services.chat_store.sessionmaker", return_value=mock_session),
        ):
            store = PostgresChatStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            messages = store.get_messages(client_guid, user_guid)

            assert len(messages) == len(expected_messages)
            assert all(isinstance(msg, ChatMessage) for msg in messages)
            # Verify content matches
            for actual, expected in zip(messages, expected_messages):
                assert actual.content == expected.content
                assert actual.role == expected.role

    def test_add_message_sync(self, mock_chat_store_session, test_uuids: dict[str, uuid.UUID], chat_store_test_data: dict[str, Any]):
        """Test adding a single message synchronously."""
        mock_session, mock_session_instance = mock_chat_store_session
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]
        message = chat_store_test_data["simple_messages"][0]

        with (
            patch("common.services.chat_store.create_engine"),
            patch("common.services.chat_store.create_async_engine"),
            patch("common.services.chat_store.sessionmaker", return_value=mock_session),
        ):
            store = PostgresChatStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            # Mock the table class to verify proper object creation
            mock_table_instance = MagicMock()
            store._table_class = MagicMock(return_value=mock_table_instance)

            store.add_message(client_guid, user_guid, message)

            # Verify table instance was created with correct data
            store._table_class.assert_called_once()
            call_kwargs = store._table_class.call_args[1]
            assert call_kwargs["client_guid"] == client_guid
            assert call_kwargs["user_guid"] == user_guid
            assert call_kwargs["message"] == message.model_dump()
            assert "timestamp" in call_kwargs

            # Verify session operations
            mock_session_instance.add.assert_called_once_with(mock_table_instance)
            mock_session_instance.commit.assert_called()


class TestChatStoreAsyncOperations:
    """Test asynchronous operations."""

    @pytest.mark.asyncio
    async def test_async_get_messages_empty_result(self, mock_chat_store_async_session, test_uuids: dict[str, uuid.UUID]):
        """Test async message retrieval with empty result."""
        mock_async_session, mock_async_session_instance = mock_chat_store_async_session
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # Mock async query result with proper SQLAlchemy pattern
        mock_result = MagicMock()
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = []
        mock_result.scalars.return_value = mock_scalars
        mock_async_session_instance.execute.return_value = mock_result

        with (
            patch("common.services.chat_store.create_engine"),
            patch("common.services.chat_store.create_async_engine"),
            patch("common.services.chat_store.sessionmaker") as mock_sessionmaker,
        ):
            # Mock both sync and async sessionmaker calls
            mock_sync_session = MagicMock()
            mock_sync_session_instance = MagicMock()
            mock_sync_session.return_value.__enter__.return_value = mock_sync_session_instance
            mock_sessionmaker.side_effect = [mock_sync_session, mock_async_session]

            store = PostgresChatStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            messages = await store.aget_messages(client_guid, user_guid)

            assert messages == []
            assert isinstance(messages, list)

    @pytest.mark.asyncio
    async def test_async_add_message(
        self, mock_chat_store_async_session, test_uuids: dict[str, uuid.UUID], chat_store_test_data: dict[str, Any]
    ):
        """Test adding a message asynchronously."""
        mock_async_session, mock_async_session_instance = mock_chat_store_async_session
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]
        message = chat_store_test_data["simple_messages"][0]

        with (
            patch("common.services.chat_store.create_engine"),
            patch("common.services.chat_store.create_async_engine"),
            patch("common.services.chat_store.sessionmaker") as mock_sessionmaker,
        ):
            # Mock both sync and async sessionmaker calls
            mock_sync_session = MagicMock()
            mock_sync_session_instance = MagicMock()
            mock_sync_session.return_value.__enter__.return_value = mock_sync_session_instance
            mock_sessionmaker.side_effect = [mock_sync_session, mock_async_session]

            store = PostgresChatStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            # Mock the table class
            mock_table_instance = MagicMock()
            store._table_class = MagicMock(return_value=mock_table_instance)

            await store.aadd_message(client_guid, user_guid, message)

            # Verify table instance was created with correct data
            store._table_class.assert_called_once()
            call_kwargs = store._table_class.call_args[1]
            assert call_kwargs["client_guid"] == client_guid
            assert call_kwargs["user_guid"] == user_guid
            assert call_kwargs["message"] == message.model_dump()

            # Verify async session operations
            mock_async_session_instance.add.assert_called_once_with(mock_table_instance)
            mock_async_session_instance.commit.assert_called_once()


class TestChatStoreEdgeCases:
    """Test edge cases and error scenarios."""

    @pytest.mark.parametrize("edge_case_key", ["very_long_message", "special_characters", "newlines_and_tabs"])
    def test_edge_case_data_handling(
        self, mock_chat_store_session, chat_store_edge_cases: dict[str, Any], test_uuids: dict[str, uuid.UUID], edge_case_key: str
    ):
        """Test handling of edge case data."""
        mock_session, mock_session_instance = mock_chat_store_session
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]
        edge_messages = chat_store_edge_cases[edge_case_key]

        with (
            patch("common.services.chat_store.create_engine"),
            patch("common.services.chat_store.create_async_engine"),
            patch("common.services.chat_store.sessionmaker", return_value=mock_session),
        ):
            store = PostgresChatStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            store._table_class = MagicMock()

            # Test that edge case messages can be processed
            for message in edge_messages:
                store.add_message(client_guid, user_guid, message)

            # Verify each message was processed
            assert store._table_class.call_count == len(edge_messages)
            assert mock_session_instance.add.call_count == len(edge_messages)

    def test_database_integrity_error_handling(
        self, mock_chat_store_session, chat_store_test_data: dict[str, Any], test_uuids: dict[str, uuid.UUID]
    ):
        """Test handling of database integrity errors."""
        mock_session, mock_session_instance = mock_chat_store_session
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]
        message = chat_store_test_data["simple_messages"][0]

        with (
            patch("common.services.chat_store.create_engine"),
            patch("common.services.chat_store.create_async_engine"),
            patch("common.services.chat_store.sessionmaker", return_value=mock_session),
        ):
            store = PostgresChatStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            store._table_class = MagicMock()

            # Set side effect after initialization to target only message operations
            mock_session_instance.commit.side_effect = IntegrityError("Constraint violation", {}, None)

            with pytest.raises(IntegrityError):
                store.add_message(client_guid, user_guid, message)


class TestChatStoreDataIntegrity:
    """Test data integrity and special cases."""

    def test_empty_conversation_handling(self, mock_chat_store_session, test_uuids: dict[str, uuid.UUID]):
        """Test handling of empty conversations."""
        mock_session, mock_session_instance = mock_chat_store_session
        client_guid = test_uuids["client_1"]
        user_guid = test_uuids["user_1"]

        # Mock empty result
        mock_result = MagicMock()
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = []
        mock_result.scalars.return_value = mock_scalars
        mock_session_instance.execute.return_value = mock_result

        with (
            patch("common.services.chat_store.create_engine"),
            patch("common.services.chat_store.create_async_engine"),
            patch("common.services.chat_store.sessionmaker", return_value=mock_session),
        ):
            store = PostgresChatStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            messages = store.get_messages(client_guid, user_guid)

            assert messages == []
            assert len(messages) == 0


class TestChatStoreSchemaInitialization:
    """Test schema and table initialization logic."""

    def test_schema_creation_logic(self, mock_chat_store_session):
        """Test schema creation when it doesn't exist."""
        mock_session, mock_session_instance = mock_chat_store_session

        # Mock schema check to return no result (schema doesn't exist)
        mock_session_instance.execute.return_value.fetchone.return_value = None

        with (
            patch("common.services.chat_store.create_engine"),
            patch("common.services.chat_store.create_async_engine"),
            patch("common.services.chat_store.sessionmaker", return_value=mock_session),
        ):
            store = PostgresChatStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            # Verify schema check was performed during initialization
            mock_session_instance.execute.assert_called()
            mock_session_instance.commit.assert_called()

    def test_existing_schema_handling(self, mock_chat_store_session):
        """Test handling when schema already exists."""
        mock_session, mock_session_instance = mock_chat_store_session

        # Mock schema check to return a result (schema exists)
        mock_result = MagicMock()
        mock_session_instance.execute.return_value.fetchone.return_value = mock_result

        with (
            patch("common.services.chat_store.create_engine"),
            patch("common.services.chat_store.create_async_engine"),
            patch("common.services.chat_store.sessionmaker", return_value=mock_session),
        ):
            store = PostgresChatStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            # Verify initialization was completed
            mock_session_instance.execute.assert_called()
            mock_session_instance.commit.assert_called()
