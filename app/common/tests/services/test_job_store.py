"""Tests for JobStore service following best practices."""

import uuid
from datetime import datetime
from typing import Any
from unittest.mock import MagicMock, patch

import pytest
from common.services import JobStore
from common.services.job_store import BatchJobStatusEnum, Job
from sqlalchemy.exc import IntegrityError

from ..fixtures.constants import (
    TEST_CUSTOM_JOB_SCHEMA_NAME,
    TEST_CUSTOM_JOB_TABLE_NAME,
    TEST_DB_HOST,
    TEST_DB_NAME,
    TEST_DB_PASSWORD,
    TEST_DB_PORT,
    TEST_DB_URI,
    TEST_DB_USER,
    TEST_JOB_SCHEMA_NAME,
    TEST_JOB_TABLE_NAME,
    TEST_URI_JOB_TABLE_NAME,
)


class TestJobStoreCreation:
    """Test JobStore instantiation and configuration."""

    @patch("common.services.job_store.create_engine")
    @patch("common.services.job_store.create_async_engine")
    @patch("common.services.job_store.sessionmaker")
    def test_from_params_with_defaults(self, mock_sessionmaker: Any, mock_create_async_engine: Any, mock_create_engine: Any):
        """Test JobStore creation with default parameters."""
        store = JobStore.from_params(
            host=TEST_DB_HOST,
            port=TEST_DB_PORT,
            database=TEST_DB_NAME,
            user=TEST_DB_USER,
            password=TEST_DB_PASSWORD,
        )

        assert store.table_name == TEST_JOB_TABLE_NAME
        assert store.schema_name == TEST_JOB_SCHEMA_NAME

    @patch("common.services.job_store.create_engine")
    @patch("common.services.job_store.create_async_engine")
    @patch("common.services.job_store.sessionmaker")
    def test_from_params_with_custom_values(self, mock_sessionmaker: Any, mock_create_async_engine: Any, mock_create_engine: Any):
        """Test JobStore creation with custom table and schema names."""
        store = JobStore.from_params(
            host=TEST_DB_HOST,
            port=TEST_DB_PORT,
            database=TEST_DB_NAME,
            user=TEST_DB_USER,
            password=TEST_DB_PASSWORD,
            table_name=TEST_CUSTOM_JOB_TABLE_NAME,
            schema_name=TEST_CUSTOM_JOB_SCHEMA_NAME,
            debug=True,
            use_jsonb=True,
        )

        assert store.table_name == TEST_CUSTOM_JOB_TABLE_NAME
        assert store.schema_name == TEST_CUSTOM_JOB_SCHEMA_NAME

    @patch("common.services.job_store.create_engine")
    @patch("common.services.job_store.create_async_engine")
    @patch("common.services.job_store.sessionmaker")
    def test_from_uri_creation(self, mock_sessionmaker: Any, mock_create_async_engine: Any, mock_create_engine: Any):
        """Test JobStore creation from URI."""
        store = JobStore.from_uri(
            uri=TEST_DB_URI,
            db_name=TEST_DB_NAME,
            table_name=TEST_URI_JOB_TABLE_NAME,
            debug=True,
            use_jsonb=True,
        )

        assert store.table_name == TEST_URI_JOB_TABLE_NAME
        assert store.schema_name == TEST_JOB_SCHEMA_NAME


class TestJobStoreCRUDOperations:
    """Test Create, Read, Update, Delete operations."""

    def test_create_job_success(self, mock_job_store_session, mock_job_database_table, job_store_test_data, test_uuids):
        """Test successful job creation."""
        mock_session, mock_session_instance = mock_job_store_session
        job_data = job_store_test_data["simple_job"]

        # Create mock entry
        mock_entry = mock_job_database_table(
            test_uuids["job_1"], job_data["client_guid"], job_data["total_count"], job_data["job_params"], job_data["status"]
        )

        with (
            patch("common.services.job_store.create_engine"),
            patch("common.services.job_store.create_async_engine"),
            patch("common.services.job_store.sessionmaker", return_value=mock_session),
        ):
            store = JobStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock(return_value=mock_entry)

            job_id = store.create_job(
                client_guid=job_data["client_guid"],
                total_count=job_data["total_count"],
                job_params=job_data["job_params"],
                status=job_data["status"],
            )

            assert job_id == test_uuids["job_1"]
            mock_session_instance.add.assert_called_once()
            # Commit is called during initialization and job creation
            assert mock_session_instance.commit.call_count >= 1

    def test_get_job_success(self, mock_job_store_session, mock_job_database_table, job_store_test_data, test_uuids):
        """Test successful job retrieval."""
        mock_session, mock_session_instance = mock_job_store_session
        job_data = job_store_test_data["complex_job"]

        # Create mock entry
        mock_entry = mock_job_database_table(
            test_uuids["job_1"], job_data["client_guid"], job_data["total_count"], job_data["job_params"], job_data["status"]
        )
        mock_session_instance.query.return_value.filter_by.return_value.first.return_value = mock_entry

        with (
            patch("common.services.job_store.create_engine"),
            patch("common.services.job_store.create_async_engine"),
            patch("common.services.job_store.sessionmaker", return_value=mock_session),
        ):
            store = JobStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            retrieved_job = store.get_job(test_uuids["job_1"])

            assert isinstance(retrieved_job, Job)
            assert retrieved_job.id == test_uuids["job_1"]
            assert retrieved_job.client_guid == job_data["client_guid"]
            assert retrieved_job.total_count == job_data["total_count"]
            assert retrieved_job.job_params == job_data["job_params"]
            assert retrieved_job.status == job_data["status"]
            assert isinstance(retrieved_job.created_at, datetime)
            assert isinstance(retrieved_job.updated_at, datetime)

    def test_get_nonexistent_job_raises_lookup_error(self, mock_job_store_session, test_uuids):
        """Test that retrieving non-existent job raises LookupError."""
        mock_session, mock_session_instance = mock_job_store_session
        mock_session_instance.query.return_value.filter_by.return_value.first.return_value = None

        with (
            patch("common.services.job_store.create_engine"),
            patch("common.services.job_store.create_async_engine"),
            patch("common.services.job_store.sessionmaker", return_value=mock_session),
        ):
            store = JobStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            with pytest.raises(LookupError, match="Job not found"):
                store.get_job(test_uuids["job_1"])

    @pytest.mark.parametrize("job_key", ["simple_job", "complex_job", "minimal_job", "completed_job"])
    def test_create_multiple_job_types(self, mock_job_store_session, mock_job_database_table, job_store_test_data, test_uuids, job_key):
        """Test creating different types of jobs."""
        mock_session, mock_session_instance = mock_job_store_session
        job_data = job_store_test_data[job_key]

        mock_entry = mock_job_database_table(
            test_uuids["job_1"], job_data["client_guid"], job_data["total_count"], job_data["job_params"], job_data["status"]
        )

        with (
            patch("common.services.job_store.create_engine"),
            patch("common.services.job_store.create_async_engine"),
            patch("common.services.job_store.sessionmaker", return_value=mock_session),
        ):
            store = JobStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock(return_value=mock_entry)

            job_id = store.create_job(
                client_guid=job_data["client_guid"],
                total_count=job_data["total_count"],
                job_params=job_data["job_params"],
                status=job_data["status"],
            )

            assert job_id == test_uuids["job_1"]
            mock_session_instance.add.assert_called_once()
            # Commit is called during initialization and job creation
            assert mock_session_instance.commit.call_count >= 1


class TestJobStoreAsyncOperations:
    """Test asynchronous operations."""

    @pytest.mark.asyncio
    async def test_async_create_job_success(self, mock_job_store_async_session, mock_job_database_table, job_store_test_data, test_uuids):
        """Test successful async job creation."""
        mock_async_session, mock_async_session_instance = mock_job_store_async_session
        job_data = job_store_test_data["simple_job"]

        mock_entry = mock_job_database_table(
            test_uuids["job_1"], job_data["client_guid"], job_data["total_count"], job_data["job_params"], job_data["status"]
        )

        with (
            patch("common.services.job_store.create_engine"),
            patch("common.services.job_store.create_async_engine"),
            patch("common.services.job_store.sessionmaker") as mock_sessionmaker,
        ):
            # Mock both sync and async sessionmaker calls
            mock_sessionmaker.side_effect = [MagicMock(), mock_async_session]

            store = JobStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock(return_value=mock_entry)

            job_id = await store.async_create_job(
                client_guid=job_data["client_guid"],
                total_count=job_data["total_count"],
                job_params=job_data["job_params"],
                status=job_data["status"],
            )

            assert job_id == test_uuids["job_1"]
            mock_async_session_instance.add.assert_called_once()
            # Commit should be called (async mock doesn't need await for assertion)
            mock_async_session_instance.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_get_job_success(self, mock_job_store_async_session, mock_job_database_table, job_store_test_data, test_uuids):
        """Test successful async job retrieval."""
        mock_async_session, mock_async_session_instance = mock_job_store_async_session
        job_data = job_store_test_data["complex_job"]

        mock_entry = mock_job_database_table(
            test_uuids["job_1"], job_data["client_guid"], job_data["total_count"], job_data["job_params"], job_data["status"]
        )

        # Mock async query result
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_entry
        mock_async_session_instance.execute.return_value = mock_result

        with (
            patch("common.services.job_store.create_engine"),
            patch("common.services.job_store.create_async_engine"),
            patch("common.services.job_store.sessionmaker") as mock_sessionmaker,
        ):
            # Mock both sync and async sessionmaker calls
            mock_sessionmaker.side_effect = [MagicMock(), mock_async_session]

            store = JobStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            retrieved_job = await store.async_get_job(test_uuids["job_1"])

            assert isinstance(retrieved_job, Job)
            assert retrieved_job.id == test_uuids["job_1"]
            assert retrieved_job.client_guid == job_data["client_guid"]
            assert retrieved_job.total_count == job_data["total_count"]
            assert retrieved_job.job_params == job_data["job_params"]

    @pytest.mark.asyncio
    async def test_async_get_nonexistent_job_raises_error(self, mock_job_store_async_session, test_uuids):
        """Test async retrieval of non-existent job raises LookupError."""
        mock_async_session, mock_async_session_instance = mock_job_store_async_session

        # Mock empty result
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_async_session_instance.execute.return_value = mock_result

        with (
            patch("common.services.job_store.create_engine"),
            patch("common.services.job_store.create_async_engine"),
            patch("common.services.job_store.sessionmaker") as mock_sessionmaker,
        ):
            mock_sessionmaker.side_effect = [MagicMock(), mock_async_session]

            store = JobStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            with pytest.raises(LookupError, match="Job not found"):
                await store.async_get_job(test_uuids["job_1"])


class TestJobStoreStatusManagement:
    """Test job status management and enum values."""

    def test_job_status_enum_values(self):
        """Test that job status enum has expected values."""
        assert BatchJobStatusEnum.PENDING == 0
        assert BatchJobStatusEnum.PROCESSING == 1
        assert BatchJobStatusEnum.COMPLETED == 2
        assert BatchJobStatusEnum.FAILED == 3
        assert BatchJobStatusEnum.PARTIALLY_COMPLETED == 4

    @pytest.mark.parametrize(
        "status",
        [
            BatchJobStatusEnum.PENDING,
            BatchJobStatusEnum.PROCESSING,
            BatchJobStatusEnum.COMPLETED,
            BatchJobStatusEnum.FAILED,
            BatchJobStatusEnum.PARTIALLY_COMPLETED,
        ],
    )
    def test_job_creation_with_different_statuses(
        self, mock_job_store_session, mock_job_database_table, job_store_test_data, test_uuids, status
    ):
        """Test creating jobs with different status values."""
        mock_session, mock_session_instance = mock_job_store_session
        job_data = job_store_test_data["simple_job"]

        mock_entry = mock_job_database_table(
            test_uuids["job_1"], job_data["client_guid"], job_data["total_count"], job_data["job_params"], status
        )

        with (
            patch("common.services.job_store.create_engine"),
            patch("common.services.job_store.create_async_engine"),
            patch("common.services.job_store.sessionmaker", return_value=mock_session),
        ):
            store = JobStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock(return_value=mock_entry)

            job_id = store.create_job(
                client_guid=job_data["client_guid"], total_count=job_data["total_count"], job_params=job_data["job_params"], status=status
            )

            assert job_id == test_uuids["job_1"]


class TestJobStoreEdgeCases:
    """Test edge cases and error scenarios."""

    @pytest.mark.parametrize("edge_case_key", ["large_params", "unicode_params", "zero_count", "no_client"])
    def test_edge_case_data_handling(
        self, mock_job_store_session, mock_job_database_table, job_store_edge_cases, test_uuids, edge_case_key
    ):
        """Test handling of edge case data."""
        mock_session, mock_session_instance = mock_job_store_session
        edge_data = job_store_edge_cases[edge_case_key]

        mock_entry = mock_job_database_table(
            test_uuids["job_1"], edge_data["client_guid"], edge_data["total_count"], edge_data["job_params"], edge_data["status"]
        )

        with (
            patch("common.services.job_store.create_engine"),
            patch("common.services.job_store.create_async_engine"),
            patch("common.services.job_store.sessionmaker", return_value=mock_session),
        ):
            store = JobStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock(return_value=mock_entry)

            job_id = store.create_job(
                client_guid=edge_data["client_guid"],
                total_count=edge_data["total_count"],
                job_params=edge_data["job_params"],
                status=edge_data["status"],
            )

            assert job_id == test_uuids["job_1"]

    def test_database_integrity_error_handling(self, mock_job_store_session, job_store_test_data):
        """Test handling of database integrity errors."""
        mock_session, mock_session_instance = mock_job_store_session
        job_data = job_store_test_data["simple_job"]

        with (
            patch("common.services.job_store.create_engine"),
            patch("common.services.job_store.create_async_engine"),
            patch("common.services.job_store.sessionmaker", return_value=mock_session),
        ):
            store = JobStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock()

            # Set side effect after initialization to target only job creation
            mock_session_instance.commit.side_effect = IntegrityError("Duplicate key", {}, Exception("Duplicate"))

            with pytest.raises(IntegrityError):
                store.create_job(
                    client_guid=job_data["client_guid"], total_count=job_data["total_count"], job_params=job_data["job_params"]
                )


class TestJobStoreSchemaInitialization:
    """Test schema and table initialization logic."""

    def test_schema_creation_logic(self, mock_job_store_session):
        """Test schema creation when it doesn't exist."""
        mock_session, mock_session_instance = mock_job_store_session

        # Mock schema check to return no result (schema doesn't exist)
        mock_session_instance.execute.return_value.fetchone.return_value = None

        with (
            patch("common.services.job_store.create_engine"),
            patch("common.services.job_store.create_async_engine"),
            patch("common.services.job_store.sessionmaker", return_value=mock_session),
        ):
            store = JobStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            # Verify schema check was performed
            mock_session_instance.execute.assert_called()
            # Verify commit was called (for schema creation)
            mock_session_instance.commit.assert_called()

    def test_existing_schema_handling(self, mock_job_store_session):
        """Test handling when schema already exists."""
        mock_session, mock_session_instance = mock_job_store_session

        # Mock schema check to return a result (schema exists)
        mock_result = MagicMock()
        mock_session_instance.execute.return_value.fetchone.return_value = mock_result

        with (
            patch("common.services.job_store.create_engine"),
            patch("common.services.job_store.create_async_engine"),
            patch("common.services.job_store.sessionmaker", return_value=mock_session),
        ):
            store = JobStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            # Verify schema check was performed
            mock_session_instance.execute.assert_called()
            # Verify commit was still called (for potential table creation)
            mock_session_instance.commit.assert_called()


class TestJobStoreDataIntegrity:
    """Test data integrity and validation scenarios."""

    def test_client_job_isolation(self, mock_job_store_session, mock_job_database_table, job_store_test_data, test_uuids):
        """Test that jobs are properly isolated by client."""
        mock_session, mock_session_instance = mock_job_store_session

        # Create jobs for different clients
        client1_job = job_store_test_data["simple_job"]
        client2_job = job_store_test_data["minimal_job"]

        mock_entry1 = mock_job_database_table(
            test_uuids["job_1"], client1_job["client_guid"], client1_job["total_count"], client1_job["job_params"]
        )

        mock_entry2 = mock_job_database_table(
            uuid.uuid4(),  # Different job ID
            client2_job["client_guid"],
            client2_job["total_count"],
            client2_job["job_params"],
        )

        with (
            patch("common.services.job_store.create_engine"),
            patch("common.services.job_store.create_async_engine"),
            patch("common.services.job_store.sessionmaker", return_value=mock_session),
        ):
            store = JobStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            # Test that different clients have different jobs
            assert client1_job["client_guid"] != client2_job["client_guid"]
            assert mock_entry1.client_guid != mock_entry2.client_guid

    def test_job_parameter_validation(self, mock_job_store_session, mock_job_database_table, test_uuids):
        """Test validation of complex job parameters."""
        mock_session, mock_session_instance = mock_job_store_session

        complex_params = {
            "model_config": {"model": "gpt-4", "temperature": 0.7, "max_tokens": 1000, "system_prompt": "You are a helpful assistant"},
            "batch_config": {"batch_size": 10, "retry_count": 3, "timeout": 300},
            "output_config": {"format": "json", "include_metadata": True, "compression": "gzip"},
        }

        mock_entry = mock_job_database_table(test_uuids["job_1"], test_uuids["client_1"], 100, complex_params)

        with (
            patch("common.services.job_store.create_engine"),
            patch("common.services.job_store.create_async_engine"),
            patch("common.services.job_store.sessionmaker", return_value=mock_session),
        ):
            store = JobStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock(return_value=mock_entry)

            job_id = store.create_job(client_guid=test_uuids["client_1"], total_count=100, job_params=complex_params)

            assert job_id == test_uuids["job_1"]
            mock_session_instance.add.assert_called_once()
