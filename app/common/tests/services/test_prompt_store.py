"""Tests for PromptStore service following best practices."""

from datetime import datetime
from typing import Any
from unittest.mock import MagicMock, patch

import pytest
from common.services import PromptStore
from common.services.prompt_store import PromptEntry
from sqlalchemy.exc import IntegrityError

from ..fixtures.constants import (
    TEST_CUSTOM_PROMPT_SCHEMA_NAME,
    TEST_CUSTOM_PROMPT_TABLE_NAME,
    TEST_DB_HOST,
    TEST_DB_NAME,
    TEST_DB_PASSWORD,
    TEST_DB_PORT,
    TEST_DB_URI,
    TEST_DB_USER,
    TEST_PROMPT_SCHEMA_NAME,
    TEST_PROMPT_TABLE_NAME,
    TEST_URI_PROMPT_TABLE_NAME,
)


class TestPromptStoreCreation:
    """Test PromptStore instantiation and configuration."""

    @patch("common.services.prompt_store.create_engine")
    @patch("common.services.prompt_store.create_async_engine")
    @patch("common.services.prompt_store.sessionmaker")
    def test_from_params_with_defaults(self, mock_sessionmaker: Any, mock_create_async_engine: Any, mock_create_engine: Any):
        """Test PromptStore creation with default parameters."""
        store = PromptStore.from_params(
            host=TEST_DB_HOST,
            port=TEST_DB_PORT,
            database=TEST_DB_NAME,
            user=TEST_DB_USER,
            password=TEST_DB_PASSWORD,
        )

        assert store.table_name == TEST_PROMPT_TABLE_NAME
        assert store.schema_name == TEST_PROMPT_SCHEMA_NAME

    @patch("common.services.prompt_store.create_engine")
    @patch("common.services.prompt_store.create_async_engine")
    @patch("common.services.prompt_store.sessionmaker")
    def test_from_params_with_custom_values(self, mock_sessionmaker: Any, mock_create_async_engine: Any, mock_create_engine: Any):
        """Test PromptStore creation with custom table and schema names."""
        store = PromptStore.from_params(
            host=TEST_DB_HOST,
            port=TEST_DB_PORT,
            database=TEST_DB_NAME,
            user=TEST_DB_USER,
            password=TEST_DB_PASSWORD,
            table_name=TEST_CUSTOM_PROMPT_TABLE_NAME,
            schema_name=TEST_CUSTOM_PROMPT_SCHEMA_NAME,
            debug=True,
        )

        assert store.table_name == TEST_CUSTOM_PROMPT_TABLE_NAME
        assert store.schema_name == TEST_CUSTOM_PROMPT_SCHEMA_NAME

    @patch("common.services.prompt_store.create_engine")
    @patch("common.services.prompt_store.create_async_engine")
    @patch("common.services.prompt_store.sessionmaker")
    def test_from_uri_creation(self, mock_sessionmaker: Any, mock_create_async_engine: Any, mock_create_engine: Any):
        """Test PromptStore creation from URI."""
        store = PromptStore.from_uri(
            uri=TEST_DB_URI,
            db_name=TEST_DB_NAME,
            table_name=TEST_URI_PROMPT_TABLE_NAME,
            debug=True,
        )

        assert store.table_name == TEST_URI_PROMPT_TABLE_NAME
        assert store.schema_name == TEST_PROMPT_SCHEMA_NAME

    @patch("common.services.prompt_store.settings")
    @patch("common.services.prompt_store.create_engine")
    @patch("common.services.prompt_store.create_async_engine")
    @patch("common.services.prompt_store.sessionmaker")
    def test_get_instance(self, mock_sessionmaker: Any, mock_create_async_engine: Any, mock_create_engine: Any, mock_settings: Any):
        """Test singleton instance creation."""
        mock_settings.database.connection_string = TEST_DB_URI
        mock_settings.database.db_name = TEST_DB_NAME

        store = PromptStore.get_instance()
        assert store.table_name == TEST_PROMPT_TABLE_NAME


class TestPromptStoreCRUDOperations:
    """Test Create, Read, Update, Delete operations."""

    def test_create_prompt_success(self, mock_prompt_store_session, mock_prompt_database_table, prompt_store_test_data, test_uuids):
        """Test successful prompt creation."""
        mock_session, mock_session_instance = mock_prompt_store_session
        prompt_data = prompt_store_test_data["simple_prompt"]

        # Create mock entry
        mock_entry = mock_prompt_database_table(
            1,  # prompt_id
            prompt_data["client_guid"],
            prompt_data["prompt"],
        )

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker", return_value=mock_session),
        ):
            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock(return_value=mock_entry)

            prompt_id = store.create_prompt(client_guid=prompt_data["client_guid"], prompt=prompt_data["prompt"])

            assert prompt_id == 1
            mock_session_instance.add.assert_called_once()
            # Commit is called during initialization and prompt creation
            assert mock_session_instance.commit.call_count >= 1

    def test_get_prompt_success(self, mock_prompt_store_session, mock_prompt_database_table, prompt_store_test_data, test_uuids):
        """Test successful prompt retrieval."""
        mock_session, mock_session_instance = mock_prompt_store_session
        prompt_data = prompt_store_test_data["complex_prompt"]

        # Create mock entry
        mock_entry = mock_prompt_database_table(
            1,  # prompt_id
            prompt_data["client_guid"],
            prompt_data["prompt"],
        )
        mock_session_instance.query.return_value.filter_by.return_value.first.return_value = mock_entry

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker", return_value=mock_session),
        ):
            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            retrieved_prompt = store.get_prompt(1, prompt_data["client_guid"])

            assert isinstance(retrieved_prompt, PromptEntry)
            assert retrieved_prompt.id == 1
            assert retrieved_prompt.client_guid == prompt_data["client_guid"]
            assert retrieved_prompt.prompt == prompt_data["prompt"]
            assert isinstance(retrieved_prompt.created_at, datetime)
            assert isinstance(retrieved_prompt.updated_at, datetime)

    def test_get_nonexistent_prompt_raises_lookup_error(self, mock_prompt_store_session, test_uuids):
        """Test that retrieving non-existent prompt raises LookupError."""
        mock_session, mock_session_instance = mock_prompt_store_session
        mock_session_instance.query.return_value.filter_by.return_value.first.return_value = None

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker", return_value=mock_session),
        ):
            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            with pytest.raises(LookupError, match="Prompt not found"):
                store.get_prompt(999, test_uuids["client_1"])

    def test_list_prompts_success(self, mock_prompt_store_session, mock_prompt_database_table, prompt_store_test_data, test_uuids):
        """Test successful prompt listing for a client."""
        mock_session, mock_session_instance = mock_prompt_store_session
        client_guid = test_uuids["client_1"]

        # Create multiple mock prompts
        mock_prompts = [
            mock_prompt_database_table(1, client_guid, "Prompt 1"),
            mock_prompt_database_table(2, client_guid, "Prompt 2"),
        ]

        # Mock SQLAlchemy query chain
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = mock_prompts
        mock_session_instance.execute.return_value = mock_result

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker", return_value=mock_session),
        ):
            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            prompts = store.list_prompts(client_guid)

            assert len(prompts) == 2
            assert all(isinstance(p, PromptEntry) for p in prompts)
            assert all(p.client_guid == client_guid for p in prompts)

    def test_update_prompt_success(self, mock_prompt_store_session, mock_prompt_database_table, test_uuids):
        """Test successful prompt update."""
        mock_session, mock_session_instance = mock_prompt_store_session
        client_guid = test_uuids["client_1"]
        new_prompt_text = "Updated prompt text"

        # Create mock entry
        mock_entry = mock_prompt_database_table(1, client_guid, "Original prompt")
        mock_session_instance.query.return_value.filter_by.return_value.first.return_value = mock_entry

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker", return_value=mock_session),
        ):
            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            store.update_prompt(1, client_guid, new_prompt_text)

            assert mock_entry.prompt == new_prompt_text
            mock_session_instance.commit.assert_called()

    def test_delete_prompt_success(self, mock_prompt_store_session, mock_prompt_database_table, test_uuids):
        """Test successful prompt deletion."""
        mock_session, mock_session_instance = mock_prompt_store_session
        client_guid = test_uuids["client_1"]

        # Create mock entry
        mock_entry = mock_prompt_database_table(1, client_guid, "Prompt to delete")
        mock_session_instance.query.return_value.filter_by.return_value.first.return_value = mock_entry

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker", return_value=mock_session),
        ):
            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            store.delete_prompt(1, client_guid)

            mock_session_instance.delete.assert_called_once_with(mock_entry)
            mock_session_instance.commit.assert_called()

    @pytest.mark.parametrize("prompt_key", ["simple_prompt", "complex_prompt", "minimal_prompt", "system_prompt"])
    def test_create_multiple_prompt_types(self, mock_prompt_store_session, mock_prompt_database_table, prompt_store_test_data, prompt_key):
        """Test creating different types of prompts."""
        mock_session, mock_session_instance = mock_prompt_store_session
        prompt_data = prompt_store_test_data[prompt_key]

        mock_entry = mock_prompt_database_table(
            1,  # prompt_id
            prompt_data["client_guid"],
            prompt_data["prompt"],
        )

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker", return_value=mock_session),
        ):
            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock(return_value=mock_entry)

            prompt_id = store.create_prompt(client_guid=prompt_data["client_guid"], prompt=prompt_data["prompt"])

            assert prompt_id == 1
            mock_session_instance.add.assert_called_once()
            # Commit is called during initialization and prompt creation
            assert mock_session_instance.commit.call_count >= 1


class TestPromptStoreAsyncOperations:
    """Test asynchronous operations."""

    @pytest.mark.asyncio
    async def test_async_create_prompt_success(self, mock_prompt_store_async_session, mock_prompt_database_table, prompt_store_test_data):
        """Test successful async prompt creation."""
        mock_async_session, mock_async_session_instance = mock_prompt_store_async_session
        prompt_data = prompt_store_test_data["simple_prompt"]

        mock_entry = mock_prompt_database_table(
            1,  # prompt_id
            prompt_data["client_guid"],
            prompt_data["prompt"],
        )

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker") as mock_sessionmaker,
        ):
            # Mock both sync and async sessionmaker calls
            mock_sessionmaker.side_effect = [MagicMock(), mock_async_session]

            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock(return_value=mock_entry)

            prompt_id = await store.async_create_prompt(client_guid=prompt_data["client_guid"], prompt=prompt_data["prompt"])

            assert prompt_id == 1
            mock_async_session_instance.add.assert_called_once()
            mock_async_session_instance.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_async_get_prompt_success(self, mock_prompt_store_async_session, mock_prompt_database_table, prompt_store_test_data):
        """Test successful async prompt retrieval."""
        mock_async_session, mock_async_session_instance = mock_prompt_store_async_session
        prompt_data = prompt_store_test_data["complex_prompt"]

        mock_entry = mock_prompt_database_table(
            1,  # prompt_id
            prompt_data["client_guid"],
            prompt_data["prompt"],
        )

        # Mock async query result
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_entry
        mock_async_session_instance.execute.return_value = mock_result

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker") as mock_sessionmaker,
        ):
            # Mock both sync and async sessionmaker calls
            mock_sessionmaker.side_effect = [MagicMock(), mock_async_session]

            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            retrieved_prompt = await store.async_get_prompt(1, prompt_data["client_guid"])

            assert isinstance(retrieved_prompt, PromptEntry)
            assert retrieved_prompt.id == 1
            assert retrieved_prompt.client_guid == prompt_data["client_guid"]
            assert retrieved_prompt.prompt == prompt_data["prompt"]

    @pytest.mark.asyncio
    async def test_async_list_prompts_success(self, mock_prompt_store_async_session, mock_prompt_database_table, test_uuids):
        """Test successful async prompt listing."""
        mock_async_session, mock_async_session_instance = mock_prompt_store_async_session
        client_guid = test_uuids["client_1"]

        # Create multiple mock prompts
        mock_prompts = [
            mock_prompt_database_table(1, client_guid, "Async Prompt 1"),
            mock_prompt_database_table(2, client_guid, "Async Prompt 2"),
        ]

        # Mock async query result
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = mock_prompts
        mock_async_session_instance.execute.return_value = mock_result

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker") as mock_sessionmaker,
        ):
            mock_sessionmaker.side_effect = [MagicMock(), mock_async_session]

            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            prompts = await store.async_list_prompts(client_guid)

            assert len(prompts) == 2
            assert all(isinstance(p, PromptEntry) for p in prompts)

    @pytest.mark.asyncio
    async def test_async_get_nonexistent_prompt_raises_error(self, mock_prompt_store_async_session, test_uuids):
        """Test async retrieval of non-existent prompt raises LookupError."""
        mock_async_session, mock_async_session_instance = mock_prompt_store_async_session

        # Mock empty result
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_async_session_instance.execute.return_value = mock_result

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker") as mock_sessionmaker,
        ):
            mock_sessionmaker.side_effect = [MagicMock(), mock_async_session]

            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            with pytest.raises(LookupError, match="Prompt not found"):
                await store.async_get_prompt(999, test_uuids["client_1"])


class TestPromptStoreClientScoping:
    """Test client scoping and isolation."""

    def test_client_isolation(self, mock_prompt_store_session, mock_prompt_database_table, test_uuids):
        """Test that prompts are properly isolated by client."""
        mock_session, mock_session_instance = mock_prompt_store_session

        client1_guid = test_uuids["client_1"]
        client2_guid = test_uuids["client_2"]

        # Create prompts for different clients
        client1_prompts = [mock_prompt_database_table(1, client1_guid, "Client 1 prompt")]
        client2_prompts = [mock_prompt_database_table(2, client2_guid, "Client 2 prompt")]

        def mock_execute_side_effect(stmt):
            # Mock different responses based on client_guid in the query
            # This is a simplified mock - in reality, SQLAlchemy would filter properly
            mock_result = MagicMock()
            if hasattr(stmt, "whereclause"):
                # Simplified logic to return different results for different clients
                mock_result.scalars.return_value.all.return_value = client1_prompts
            else:
                mock_result.scalars.return_value.all.return_value = []
            return mock_result

        mock_session_instance.execute.side_effect = mock_execute_side_effect

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker", return_value=mock_session),
        ):
            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            # Test that different clients have different prompts
            prompts_1 = store.list_prompts(client1_guid)

            assert len(prompts_1) == 1
            assert prompts_1[0].client_guid == client1_guid

    def test_empty_client_prompt_list(self, mock_prompt_store_session, test_uuids):
        """Test handling of clients with no prompts."""
        mock_session, mock_session_instance = mock_prompt_store_session
        client_guid = test_uuids["client_1"]

        # Mock empty result
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        mock_session_instance.execute.return_value = mock_result

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker", return_value=mock_session),
        ):
            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            prompts = store.list_prompts(client_guid)

            assert prompts == []
            assert len(prompts) == 0


class TestPromptStoreEdgeCases:
    """Test edge cases and error scenarios."""

    @pytest.mark.parametrize(
        "edge_case_key", ["unicode_prompt", "large_prompt", "special_chars", "newlines_and_formatting", "empty_prompt"]
    )
    def test_edge_case_data_handling(self, mock_prompt_store_session, mock_prompt_database_table, prompt_store_edge_cases, edge_case_key):
        """Test handling of edge case data."""
        mock_session, mock_session_instance = mock_prompt_store_session
        edge_data = prompt_store_edge_cases[edge_case_key]

        mock_entry = mock_prompt_database_table(
            1,  # prompt_id
            edge_data["client_guid"],
            edge_data["prompt"],
        )

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker", return_value=mock_session),
        ):
            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock(return_value=mock_entry)

            prompt_id = store.create_prompt(client_guid=edge_data["client_guid"], prompt=edge_data["prompt"])

            assert prompt_id == 1

    def test_database_integrity_error_handling(self, mock_prompt_store_session, prompt_store_test_data):
        """Test handling of database integrity errors."""
        mock_session, mock_session_instance = mock_prompt_store_session
        prompt_data = prompt_store_test_data["simple_prompt"]

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker", return_value=mock_session),
        ):
            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock()

            # Set side effect after initialization to target only prompt creation
            mock_session_instance.commit.side_effect = IntegrityError("Duplicate key", {}, Exception("Duplicate"))

            with pytest.raises(IntegrityError):
                store.create_prompt(client_guid=prompt_data["client_guid"], prompt=prompt_data["prompt"])

    def test_update_nonexistent_prompt_raises_error(self, mock_prompt_store_session, test_uuids):
        """Test updating non-existent prompt raises LookupError."""
        mock_session, mock_session_instance = mock_prompt_store_session
        mock_session_instance.query.return_value.filter_by.return_value.first.return_value = None

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker", return_value=mock_session),
        ):
            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            with pytest.raises(LookupError, match="Prompt not found"):
                store.update_prompt(999, test_uuids["client_1"], "Updated prompt")

    def test_delete_nonexistent_prompt_raises_error(self, mock_prompt_store_session, test_uuids):
        """Test deleting non-existent prompt raises LookupError."""
        mock_session, mock_session_instance = mock_prompt_store_session
        mock_session_instance.query.return_value.filter_by.return_value.first.return_value = None

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker", return_value=mock_session),
        ):
            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            with pytest.raises(LookupError, match="Prompt not found"):
                store.delete_prompt(999, test_uuids["client_1"])


class TestPromptStoreSchemaInitialization:
    """Test schema and table initialization logic."""

    def test_schema_creation_logic(self, mock_prompt_store_session):
        """Test schema creation when it doesn't exist."""
        mock_session, mock_session_instance = mock_prompt_store_session

        # Mock schema check to return no result (schema doesn't exist)
        mock_session_instance.execute.return_value.fetchone.return_value = None

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker", return_value=mock_session),
        ):
            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            # Verify schema check was performed
            mock_session_instance.execute.assert_called()
            # Verify commit was called (for schema creation)
            mock_session_instance.commit.assert_called()

    def test_existing_schema_handling(self, mock_prompt_store_session):
        """Test handling when schema already exists."""
        mock_session, mock_session_instance = mock_prompt_store_session

        # Mock schema check to return a result (schema exists)
        mock_result = MagicMock()
        mock_session_instance.execute.return_value.fetchone.return_value = mock_result

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker", return_value=mock_session),
        ):
            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            # Verify schema check was performed
            mock_session_instance.execute.assert_called()
            # Verify commit was still called (for potential table creation)
            mock_session_instance.commit.assert_called()


class TestPromptStoreDataIntegrity:
    """Test data integrity and validation scenarios."""

    def test_prompt_content_validation(self, mock_prompt_store_session, mock_prompt_database_table, test_uuids):
        """Test validation of complex prompt content."""
        mock_session, mock_session_instance = mock_prompt_store_session

        complex_prompt = """
        System: You are an AI assistant with the following capabilities:

        1. Natural language processing
        2. Code analysis and generation
        3. Mathematical reasoning
        4. Creative writing

        Guidelines:
        - Always be helpful and accurate
        - Provide detailed explanations when requested
        - Use proper formatting and structure
        - Consider context and user intent

        Special instructions: Handle unicode: 测试 🚀, special chars: !@#$%^&*()
        """

        mock_entry = mock_prompt_database_table(
            1,  # prompt_id
            test_uuids["client_1"],
            complex_prompt,
        )

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker", return_value=mock_session),
        ):
            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )
            store._table_class = MagicMock(return_value=mock_entry)

            prompt_id = store.create_prompt(client_guid=test_uuids["client_1"], prompt=complex_prompt)

            assert prompt_id == 1
            mock_session_instance.add.assert_called_once()

    def test_multiple_prompts_for_same_client(self, mock_prompt_store_session, mock_prompt_database_table, test_uuids):
        """Test creating multiple prompts for the same client."""
        mock_session, mock_session_instance = mock_prompt_store_session
        client_guid = test_uuids["client_1"]

        # Create multiple prompts
        prompts = [
            "System prompt for general tasks",
            "Creative writing assistant prompt",
            "Technical analysis prompt",
        ]

        mock_entries = []
        for i, prompt in enumerate(prompts, 1):
            mock_entry = mock_prompt_database_table(i, client_guid, prompt)
            mock_entries.append(mock_entry)

        with (
            patch("common.services.prompt_store.create_engine"),
            patch("common.services.prompt_store.create_async_engine"),
            patch("common.services.prompt_store.sessionmaker", return_value=mock_session),
        ):
            store = PromptStore.from_params(
                host=TEST_DB_HOST, port=TEST_DB_PORT, database=TEST_DB_NAME, user=TEST_DB_USER, password=TEST_DB_PASSWORD
            )

            # Test creating multiple prompts
            for i, prompt in enumerate(prompts, 1):
                store._table_class = MagicMock(return_value=mock_entries[i - 1])
                prompt_id = store.create_prompt(client_guid=client_guid, prompt=prompt)
                assert prompt_id == i

            # Verify all prompts were added
            assert mock_session_instance.add.call_count == len(prompts)
