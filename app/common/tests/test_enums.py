"""Tests for common.enums module."""

from enum import Enum

import pytest
from common.enums import BatchJobStatusEnum


class TestBatchJobStatusEnum:
    """Test BatchJobStatusEnum."""

    def test_enum_values(self):
        """Test enum has correct values."""
        assert BatchJobStatusEnum.PENDING.value == 0
        assert BatchJobStatusEnum.PROCESSING.value == 1
        assert BatchJobStatusEnum.COMPLETED.value == 2
        assert BatchJobStatusEnum.FAILED.value == 3
        assert BatchJobStatusEnum.PARTIALLY_COMPLETED.value == 4

    def test_enum_names(self):
        """Test enum has correct names."""
        assert BatchJobStatusEnum.PENDING.name == "PENDING"
        assert BatchJobStatusEnum.PROCESSING.name == "PROCESSING"
        assert BatchJobStatusEnum.COMPLETED.name == "COMPLETED"
        assert BatchJobStatusEnum.FAILED.name == "FAILED"
        assert BatchJobStatusEnum.PARTIALLY_COMPLETED.name == "PARTIALLY_COMPLETED"

    def test_enum_inheritance(self):
        """Test enum inherits from Enum."""
        assert issubclass(BatchJobStatusEnum, Enum)
        assert isinstance(BatchJobStatusEnum.PENDING, Enum)

    def test_enum_comparison(self):
        """Test enum value comparison."""
        assert BatchJobStatusEnum.PENDING == BatchJobStatusEnum.PENDING
        assert BatchJobStatusEnum.PENDING != BatchJobStatusEnum.PROCESSING
        assert BatchJobStatusEnum.PENDING.value < BatchJobStatusEnum.PROCESSING.value

    def test_enum_iteration(self):
        """Test enum iteration."""
        expected_values = [0, 1, 2, 3, 4]
        actual_values = [status.value for status in BatchJobStatusEnum]
        assert actual_values == expected_values

    def test_enum_membership(self):
        """Test enum membership."""
        assert BatchJobStatusEnum.PENDING in BatchJobStatusEnum
        assert BatchJobStatusEnum.COMPLETED in BatchJobStatusEnum

    def test_enum_from_value(self):
        """Test creating enum from value."""
        assert BatchJobStatusEnum(0) == BatchJobStatusEnum.PENDING
        assert BatchJobStatusEnum(2) == BatchJobStatusEnum.COMPLETED

        with pytest.raises(ValueError):
            BatchJobStatusEnum(99)  # Invalid value

    def test_enum_string_representation(self):
        """Test enum string representation."""
        assert str(BatchJobStatusEnum.PENDING) == "BatchJobStatusEnum.PENDING"
        assert repr(BatchJobStatusEnum.PROCESSING) == "<BatchJobStatusEnum.PROCESSING: 1>"

    def test_enum_count(self):
        """Test enum has correct number of members."""
        assert len(BatchJobStatusEnum) == 5

    def test_enum_unique_values(self):
        """Test all enum values are unique."""
        values = [status.value for status in BatchJobStatusEnum]
        assert len(values) == len(set(values))
