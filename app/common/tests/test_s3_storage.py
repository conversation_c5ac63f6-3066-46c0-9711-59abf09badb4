"""Tests for common.s3_storage module."""

import json
import uuid
from typing import Any, Dict
from unittest.mock import Mock

import pytest
from botocore.exceptions import ClientError
from common.s3_storage import S3Storage, get_s3_storage, get_s3_storage_for_job, get_s3_storage_for_job_results

from .fixtures.constants import (
    TEST_BUCKET_NAME,
    TEST_CONTENT_TYPE,
    TEST_KEY,
)

DEFAULT_PREFIX = "batch-jobs"
DEFAULT_URL_EXPIRATION = 3600


class TestS3Storage:
    """Test S3Storage class."""

    def test_init(self, s3_storage_instance: S3Storage, s3_storage_default_instance: S3Storage) -> None:
        """Test S3Storage initialization with different parameter combinations."""
        # Test with custom bucket and custom prefix
        assert s3_storage_instance.bucket_name == TEST_BUCKET_NAME
        assert s3_storage_instance.prefix == "test-prefix"

        # Test with custom bucket and default prefix
        assert s3_storage_default_instance.bucket_name == TEST_BUCKET_NAME
        assert s3_storage_default_instance.prefix == DEFAULT_PREFIX

    def test_store_result_success(self, s3_storage_instance: S3Storage, mock_s3_client: Mock, sample_s3_data: Dict[str, Any]) -> None:
        """Test successful result storage."""
        test_data = sample_s3_data["simple"]
        s3_key = "test-item-123"

        s3_storage_instance.store_result(s3_key, test_data)

        mock_s3_client.put_object.assert_called_once_with(
            Bucket=TEST_BUCKET_NAME, Key=s3_key, Body=json.dumps(test_data), ContentType="application/json"
        )

    def test_store_result_client_error(self, s3_storage_instance: S3Storage, mock_s3_client: Mock) -> None:
        """Test result storage with client error."""
        mock_s3_client.put_object.side_effect = ClientError({"Error": {"Code": "AccessDenied", "Message": "Access denied"}}, "PutObject")

        with pytest.raises(ClientError):
            s3_storage_instance.store_result("test-item", {"data": "test"})

    def test_store_result_generic_exception(self, s3_storage_instance: S3Storage, mock_s3_client: Mock) -> None:
        """Test result storage with generic exception."""
        mock_s3_client.put_object.side_effect = Exception("Generic error")

        with pytest.raises(Exception):
            s3_storage_instance.store_result("test-item", {"data": "test"})

    def test_exists_true(self, s3_storage_instance: S3Storage, mock_s3_client: Mock, s3_test_keys: Dict[str, str]) -> None:
        """Test exists method when object exists."""
        result = s3_storage_instance.exists(s3_test_keys["simple"])

        mock_s3_client.head_object.assert_called_once_with(Bucket=TEST_BUCKET_NAME, Key=s3_test_keys["simple"])
        assert result is True

    def test_exists_false(self, s3_storage_instance: S3Storage, mock_s3_client: Mock, s3_test_keys: Dict[str, str]) -> None:
        """Test exists method when object doesn't exist."""
        mock_s3_client.head_object.side_effect = ClientError({"Error": {"Code": "404", "Message": "Not found"}}, "HeadObject")

        result = s3_storage_instance.exists(s3_test_keys["nonexistent"])

        assert result is False

    def test_exists_client_error_non_404(self, s3_storage_instance: S3Storage, mock_s3_client: Mock) -> None:
        """Test exists method with non-404 client error (should raise)."""
        mock_s3_client.head_object.side_effect = ClientError({"Error": {"Code": "AccessDenied", "Message": "Access denied"}}, "HeadObject")

        with pytest.raises(ClientError):
            s3_storage_instance.exists("some-key")

    def test_exists_generic_exception(self, s3_storage_instance: S3Storage, mock_s3_client: Mock) -> None:
        """Test exists method with generic exception (should raise)."""
        mock_s3_client.head_object.side_effect = Exception("Generic error")

        with pytest.raises(Exception):
            s3_storage_instance.exists("some-key")

    def test_get_job_prefix(self, s3_storage_default_instance: S3Storage, sample_job_ids: Dict[str, str]) -> None:
        """Test get_job_prefix method."""
        job_id = sample_job_ids["string_job"]
        result = s3_storage_default_instance.get_job_prefix(job_id)

        assert result == f"{DEFAULT_PREFIX}/jobs/{job_id}"

    def test_upload_file_success(self, s3_storage_instance: S3Storage, mock_s3_client: Mock, s3_test_keys: Dict[str, str]) -> None:
        """Test successful file upload."""
        test_data = b"test file content"
        s3_key = s3_test_keys["file_upload"]

        s3_storage_instance.upload_file(s3_key, test_data, TEST_CONTENT_TYPE)

        mock_s3_client.put_object.assert_called_once_with(
            Bucket=TEST_BUCKET_NAME, Key=s3_key, Body=test_data, ContentType=TEST_CONTENT_TYPE
        )

    def test_upload_file_error(self, s3_storage_instance: S3Storage, mock_s3_client: Mock, s3_test_keys: Dict[str, str]) -> None:
        """Test file upload with error."""
        mock_s3_client.put_object.side_effect = Exception("Upload failed")
        test_data = b"test file content"

        with pytest.raises(Exception):
            s3_storage_instance.upload_file(s3_test_keys["file_upload"], test_data, TEST_CONTENT_TYPE)

    def test_get_presigned_url_success(self, s3_storage_instance: S3Storage, mock_s3_client: Mock) -> None:
        """Test successful presigned URL generation."""
        expected_url = f"https://{TEST_BUCKET_NAME}.s3.amazonaws.com/{TEST_KEY}?signature=abc123"
        mock_s3_client.generate_presigned_url.return_value = expected_url

        result = s3_storage_instance.get_presigned_url(TEST_KEY, expiration=7200)

        mock_s3_client.generate_presigned_url.assert_called_once_with(
            "get_object", Params={"Bucket": TEST_BUCKET_NAME, "Key": TEST_KEY}, ExpiresIn=7200
        )
        assert result == expected_url

    def test_get_presigned_url_default_expiration(self, s3_storage_instance: S3Storage, mock_s3_client: Mock) -> None:
        """Test presigned URL generation with default expiration."""
        mock_s3_client.generate_presigned_url.return_value = "test-url"

        s3_storage_instance.get_presigned_url(TEST_KEY)

        mock_s3_client.generate_presigned_url.assert_called_once_with(
            "get_object",
            Params={"Bucket": TEST_BUCKET_NAME, "Key": TEST_KEY},
            ExpiresIn=DEFAULT_URL_EXPIRATION,
        )

    def test_get_presigned_url_client_error(self, s3_storage_instance: S3Storage, mock_s3_client: Mock) -> None:
        """Test presigned URL generation with client error."""
        mock_s3_client.generate_presigned_url.side_effect = ClientError(
            {"Error": {"Code": "AccessDenied", "Message": "Access denied"}}, "GeneratePresignedUrl"
        )

        result = s3_storage_instance.get_presigned_url(TEST_KEY)

        assert result is None


class TestGetS3Storage:
    """Test get_s3_storage function."""

    def test_get_s3_storage(self, mock_s3_client: Mock) -> None:
        """Test get_s3_storage function."""
        result = get_s3_storage("custom-prefix")

        assert result.prefix == "custom-prefix"
        assert isinstance(result, S3Storage)


class TestGetS3StorageForJob:
    """Test get_s3_storage_for_job function."""

    def test_get_s3_storage_for_job(self, mock_s3_client: Mock, sample_job_ids: Dict[str, str]) -> None:
        """Test get_s3_storage_for_job function."""
        job_id = sample_job_ids["string_job"]

        result = get_s3_storage_for_job(job_id)

        assert result.prefix == f"batch-jobs/jobs/{job_id}"
        assert isinstance(result, S3Storage)


class TestGetS3StorageForJobResults:
    """Test get_s3_storage_for_job_results function."""

    def test_get_s3_storage_for_job_results(self, mock_s3_client: Mock, sample_client_guid: uuid.UUID) -> None:
        """Test get_s3_storage_for_job_results function."""
        result = get_s3_storage_for_job_results(sample_client_guid)

        assert result.prefix == f"batch-jobs/jobs/{sample_client_guid}/results"
        assert isinstance(result, S3Storage)
