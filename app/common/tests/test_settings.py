"""Tests for common.settings module."""

import os
from unittest.mock import Mock, patch

import pytest
from common.settings import (
    AppSettings,
    CorsSettings,
    DatabaseSettings,
    LlmGatewaySettings,
    LlmSettings,
    OTLPSettings,
    TaskProcesssorSettings,
    settings,
)
from pydantic import ValidationError


class TestDatabaseSettings:
    """Test DatabaseSettings class."""

    def test_default_values(self):
        """Test default database settings values."""
        db_settings = DatabaseSettings()

        assert db_settings.connection_string == ""
        assert db_settings.db_name == "encore"

    def test_custom_values(self):
        """Test custom database settings values."""
        custom_connection = "**********************************/mydb"
        db_settings = DatabaseSettings(connection_string=custom_connection, db_name="custom_db")

        assert db_settings.connection_string == custom_connection
        assert db_settings.db_name == "custom_db"


class TestLlmSettings:
    """Test LlmSettings class."""

    def test_default_values(self):
        """Test default LLM settings values."""
        llm_settings = LlmSettings()

        assert llm_settings.embed_dim == 1536
        assert llm_settings.embed_model == "text-embedding-3-small"
        assert llm_settings.rag_model == "aws:anthropic.claude-3-haiku-20240307-v1:0"
        assert llm_settings.top_k == 10
        assert llm_settings.default_model == "aws:anthropic.claude-3-5-sonnet-20240620-v1:0"
        assert llm_settings.default_temperature == 0.1
        assert llm_settings.default_top_p == 0.9
        assert llm_settings.default_max_tokens == 4096

    def test_custom_values(self):
        """Test custom LLM settings values."""
        llm_settings = LlmSettings(embed_dim=768, embed_model="custom-embedding-model", default_temperature=0.5)

        assert llm_settings.embed_dim == 768
        assert llm_settings.embed_model == "custom-embedding-model"
        assert llm_settings.default_temperature == 0.5


class TestLlmGatewaySettings:
    """Test LlmGatewaySettings class."""

    def test_default_values(self):
        """Test default LLM gateway settings values."""
        gateway_settings = LlmGatewaySettings()

        assert gateway_settings.base_url == "http://llm_gateway:8008"
        assert gateway_settings.master_key == "#{LitellmMasterKey}"

    def test_custom_values(self):
        """Test custom LLM gateway settings values."""
        gateway_settings = LlmGatewaySettings(base_url="https://gateway.example.com", master_key="custom-master-key")

        assert gateway_settings.base_url == "https://gateway.example.com"
        assert gateway_settings.master_key == "custom-master-key"


class TestOTLPSettings:
    """Test OTLPSettings class."""

    def test_default_values(self):
        """Test default OTLP settings values."""
        otlp_settings = OTLPSettings()

        assert otlp_settings.otlp_headers == "api-key=xyz"
        assert otlp_settings.otlp_endpoint == "https://otlp.nr-data.net:4317"
        assert otlp_settings.otlp_protocol == "grpc"

    def test_custom_values(self):
        """Test custom OTLP settings values."""
        otlp_settings = OTLPSettings(otlp_endpoint="https://otlp.example.com:4317", otlp_protocol="http")

        assert otlp_settings.otlp_endpoint == "https://otlp.example.com:4317"
        assert otlp_settings.otlp_protocol == "http"


class TestCorsSettings:
    """Test CorsSettings class."""

    def test_default_values(self):
        """Test default CORS settings values."""
        cors_settings = CorsSettings()

        assert cors_settings.origins == ""

    def test_custom_values(self):
        """Test custom CORS settings values."""
        cors_settings = CorsSettings(origins="https://example.com;https://app.example.com")

        assert cors_settings.origins == "https://example.com;https://app.example.com"


class TestTaskProcessorSettings:
    """Test TaskProcesssorSettings class."""

    def test_default_values(self):
        """Test default task processor settings values."""
        task_settings = TaskProcesssorSettings()

        assert task_settings.task_queue_namespace == "encore-task-processor"
        assert task_settings.task_max_retries == 3
        assert task_settings.task_time_limit_ms == 180000
        assert task_settings.task_min_backoff_ms == 180000
        assert task_settings.task_max_backoff_ms == 3600000
        assert task_settings.task_visibility_timeout == 600
        assert task_settings.polling_interval_seconds == 5
        assert task_settings.batch_size == 10
        assert task_settings.bucket_name == "aca-dev-encore-us-east-1"
        assert task_settings.callback_timeout == 10
        assert task_settings.store_job_params is False

    def test_custom_values(self):
        """Test custom task processor settings values."""
        task_settings = TaskProcesssorSettings(
            task_queue_namespace="custom-namespace", task_max_retries=5, batch_size=20, bucket_name="custom-bucket", store_job_params=True
        )

        assert task_settings.task_queue_namespace == "custom-namespace"
        assert task_settings.task_max_retries == 5
        assert task_settings.batch_size == 20
        assert task_settings.bucket_name == "custom-bucket"
        assert task_settings.store_job_params is True


class TestAppSettings:
    """Test AppSettings class."""

    def test_default_initialization(self):
        """Test AppSettings initialization with defaults."""
        app_settings = AppSettings()

        assert isinstance(app_settings.database, DatabaseSettings)
        assert isinstance(app_settings.llm, LlmSettings)
        assert isinstance(app_settings.llm_gateway, LlmGatewaySettings)
        assert isinstance(app_settings.otlp, OTLPSettings)
        assert isinstance(app_settings.cors, CorsSettings)
        assert isinstance(app_settings.task_processor, TaskProcesssorSettings)

    @patch.dict(os.environ, {"is_local": "true"})
    @patch.object(AppSettings, "__new__")
    def test_get_settings_local(self, mock_new):
        """Test get_settings for local environment."""
        mock_instance = Mock(spec=AppSettings)
        mock_new.return_value = mock_instance

        result = AppSettings.get_settings()

        mock_new.assert_called_once_with(AppSettings)
        assert result == mock_instance

    @patch.dict(os.environ, {"env_name": "production"}, clear=True)
    @patch.object(AppSettings, "from_aws_parameter_store")
    def test_get_settings_aws(self, mock_from_aws):
        """Test get_settings for AWS environment."""
        mock_instance = Mock()
        mock_from_aws.return_value = mock_instance

        result = AppSettings.get_settings()

        mock_from_aws.assert_called_once_with("production")
        assert result == mock_instance

    @patch("common.settings.boto3.client")
    def test_fetch_params_string_value(self, mock_boto_client):
        """Test _fetch_params with string parameter."""
        mock_ssm = Mock()
        mock_ssm.get_parameter.return_value = {"Parameter": {"Value": "test-value"}}
        mock_boto_client.return_value = mock_ssm

        result = AppSettings._fetch_params(mock_ssm, "/test/parameter")

        assert result == "test-value"
        mock_ssm.get_parameter.assert_called_once_with(Name="/test/parameter", WithDecryption=True)

    @patch("common.settings.boto3.client")
    def test_fetch_params_dict_value(self, mock_boto_client):
        """Test _fetch_params with dictionary parameter."""
        mock_ssm = Mock()
        mock_ssm.get_parameter.side_effect = [{"Parameter": {"Value": "value1"}}, {"Parameter": {"Value": "value2"}}]
        mock_boto_client.return_value = mock_ssm

        param_dict = {"key1": "/test/param1", "key2": "/test/param2"}

        result = AppSettings._fetch_params(mock_ssm, param_dict)

        expected = {"key1": "value1", "key2": "value2"}
        assert result == expected

    def test_settings_singleton(self):
        """Test that settings is properly initialized."""
        assert isinstance(settings, AppSettings)
