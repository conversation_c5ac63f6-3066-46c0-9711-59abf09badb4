"""Tests for common.utils module."""

import os
from datetime import datetime, timezone
from unittest.mock import Mock, patch

import pytest
from botocore.exceptions import ClientError
from common.utils import get_current_time, store_in_secrets_manager


class TestGetCurrentTime:
    """Test get_current_time function."""

    def test_returns_datetime_object(self):
        """Test that get_current_time returns a datetime object."""
        result = get_current_time()
        assert isinstance(result, datetime)

    def test_returns_utc_time_without_timezone(self):
        """Test that returned time is UTC without timezone info."""
        result = get_current_time()
        assert result.tzinfo is None

    def test_time_is_recent(self):
        """Test that returned time is recent (within last few seconds)."""
        before = datetime.now(timezone.utc).replace(tzinfo=None)
        result = get_current_time()
        after = datetime.now(timezone.utc).replace(tzinfo=None)

        # Should be between before and after timestamps
        assert before <= result <= after

    def test_multiple_calls_return_different_times(self):
        """Test that multiple calls return different times."""
        time1 = get_current_time()
        # Small delay to ensure different timestamps
        import time

        time.sleep(0.001)
        time2 = get_current_time()

        assert time1 != time2
        assert time2 > time1

    def test_time_format_consistency(self):
        """Test that returned time has expected format."""
        result = get_current_time()

        # Should have year, month, day, hour, minute, second, microsecond
        assert hasattr(result, "year")
        assert hasattr(result, "month")
        assert hasattr(result, "day")
        assert hasattr(result, "hour")
        assert hasattr(result, "minute")
        assert hasattr(result, "second")
        assert hasattr(result, "microsecond")


class TestStoreInSecretsManager:
    """Test store_in_secrets_manager function."""

    @patch("common.utils.boto3.client")
    @patch.dict(os.environ, {"AWS_REGION": "us-west-2"})
    def test_create_secret_success(self, mock_boto_client):
        """Test successful secret creation."""
        mock_client = Mock()
        mock_boto_client.return_value = mock_client

        store_in_secrets_manager("test-secret", "secret-value", "Test description")

        mock_boto_client.assert_called_once_with("secretsmanager", region_name="us-west-2")
        mock_client.create_secret.assert_called_once_with(Name="test-secret", SecretString="secret-value", Description="Test description")

    @patch("common.utils.boto3.client")
    def test_create_secret_default_region(self, mock_boto_client):
        """Test secret creation with default region."""
        mock_client = Mock()
        mock_boto_client.return_value = mock_client

        store_in_secrets_manager("test-secret", "secret-value")

        mock_boto_client.assert_called_once_with(
            "secretsmanager",
            region_name="us-east-1",  # Default region
        )

    @patch("common.utils.boto3.client")
    def test_create_secret_default_description(self, mock_boto_client):
        """Test secret creation with default description."""
        mock_client = Mock()
        mock_boto_client.return_value = mock_client

        store_in_secrets_manager("test-secret", "secret-value")

        mock_client.create_secret.assert_called_once_with(
            Name="test-secret",
            SecretString="secret-value",
            Description="",  # Default empty description
        )

    @patch("common.utils.boto3.client")
    def test_update_existing_secret(self, mock_boto_client):
        """Test updating existing secret when ResourceExistsException occurs."""
        mock_client = Mock()
        mock_client.create_secret.side_effect = ClientError(
            {"Error": {"Code": "ResourceExistsException", "Message": "Secret already exists"}}, "CreateSecret"
        )
        mock_boto_client.return_value = mock_client

        store_in_secrets_manager("existing-secret", "new-value", "Updated description")

        # Should first try to create, then update when it exists
        mock_client.create_secret.assert_called_once()
        mock_client.put_secret_value.assert_called_once_with(SecretId="existing-secret", SecretString="new-value")

    @patch("common.utils.boto3.client")
    def test_other_client_error_raises(self, mock_boto_client):
        """Test that other ClientErrors are re-raised."""
        mock_client = Mock()
        mock_client.create_secret.side_effect = ClientError({"Error": {"Code": "AccessDenied", "Message": "Access denied"}}, "CreateSecret")
        mock_boto_client.return_value = mock_client

        with pytest.raises(ClientError) as exc_info:
            store_in_secrets_manager("test-secret", "secret-value")

        assert exc_info.value.response["Error"]["Code"] == "AccessDenied"

    @patch("common.utils.boto3.client")
    def test_empty_secret_name(self, mock_boto_client):
        """Test storing secret with empty name."""
        mock_client = Mock()
        mock_boto_client.return_value = mock_client

        store_in_secrets_manager("", "secret-value")

        mock_client.create_secret.assert_called_once_with(Name="", SecretString="secret-value", Description="")

    @patch("common.utils.boto3.client")
    def test_empty_secret_value(self, mock_boto_client):
        """Test storing secret with empty value."""
        mock_client = Mock()
        mock_boto_client.return_value = mock_client

        store_in_secrets_manager("test-secret", "")

        mock_client.create_secret.assert_called_once_with(Name="test-secret", SecretString="", Description="")

    @patch("common.utils.boto3.client")
    def test_none_description_uses_default(self, mock_boto_client):
        """Test that None description uses default empty string."""
        mock_client = Mock()
        mock_boto_client.return_value = mock_client

        store_in_secrets_manager("test-secret", "secret-value", None)

        mock_client.create_secret.assert_called_once_with(
            Name="test-secret",
            SecretString="secret-value",
            Description=None,  # None is passed through
        )

    @patch("common.utils.boto3.client")
    def test_long_secret_values(self, mock_boto_client):
        """Test storing secret with long values."""
        mock_client = Mock()
        mock_boto_client.return_value = mock_client

        long_secret = "x" * 10000  # Very long secret
        long_description = "y" * 1000  # Long description

        store_in_secrets_manager("test-secret", long_secret, long_description)

        mock_client.create_secret.assert_called_once_with(Name="test-secret", SecretString=long_secret, Description=long_description)
